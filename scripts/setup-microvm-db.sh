#!/bin/bash
set -e

echo "Setting up microVM database..."

# Check for required environment variables
if [ -z "$DATABASE_URL" ]; then
  echo "ERROR: DATABASE_URL is not set. Please set the DATABASE_URL environment variable."
  echo "Example: export DATABASE_URL=postgres://user:password@host:port/dbname"
  exit 1
fi

# Run the migration script
echo "Running migrations..."
npx tsx lib/microvm/db/migrate.ts

echo "MicroVM database setup complete!" 