#!/bin/bash

echo "Setting up PostgreSQL database for Prisma..."

# Check if docker is installed
if ! command -v docker &> /dev/null
then
    echo "Docker could not be found. Please install Docker first."
    exit 1
fi

# Stop and remove existing container if it exists
docker stop postgres-prisma 2>/dev/null
docker rm postgres-prisma 2>/dev/null

# Run PostgreSQL container
echo "Starting PostgreSQL container..."
docker run --name postgres-prisma -e POSTGRES_PASSWORD=postgres -e POSTGRES_USER=postgres -e POSTGRES_DB=ai_chatbot -p 5432:5432 -d postgres:15

# Wait for PostgreSQL to start
echo "Waiting for PostgreSQL to start..."
sleep 5

# Create .env file if it doesn't exist
if [ ! -f .env.local ]; then
  echo "Creating .env.local file..."
  cat > .env.local << EOF
# Database connection
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/ai_chatbot?schema=public"
POSTGRES_URL="postgresql://postgres:postgres@localhost:5432/ai_chatbot?schema=public"

# Add other environment variables here
EOF
fi

echo "PostgreSQL is running at postgresql://postgres:postgres@localhost:5432/ai_chatbot"

# Run Prisma migrations
echo "Running Prisma migrations..."
npx prisma migrate dev --name init

echo "Setup complete! Your Prisma PostgreSQL database is ready." 