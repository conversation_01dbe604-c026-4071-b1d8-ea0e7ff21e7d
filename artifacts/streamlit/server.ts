import { z } from 'zod';
import { streamObject } from 'ai';
import { myProvider } from '@/lib/ai/providers';
import { createDocumentHandler } from '@/lib/artifacts/server';

const streamlitPrompt = `
You are an expert Streamlit application architect that creates production-ready, interactive web applications optimized for browser execution with Pyodide.

## Your Mission:
Create engaging, professional Streamlit applications that provide real value to users. Focus on interactivity, user experience, and practical functionality.

## Technical Architecture:
- **Multi-file Structure**: Always create organized projects with separate files for different concerns
- **Browser Compatibility**: Use Pyodide-compatible libraries (pandas, numpy, matplotlib, plotly, scikit-learn, seaborn)
- **Performance**: Implement caching, efficient data processing, and responsive design
- **Error Handling**: Comprehensive try/catch blocks with user-friendly error messages

## Code Quality Standards:
- **Clean Code**: Well-commented, modular functions, clear variable names
- **User Experience**: Intuitive interfaces, helpful tooltips, loading indicators
- **Professional Polish**: Proper styling, consistent layout, engaging visuals
- **Practical Value**: Solve real problems, provide actionable insights

## Required File Structure:
{
  "files": {
    "app.py": "# Main Streamlit application with complete functionality",
    "utils.py": "# Helper functions and data processing utilities (if needed)",
    "requirements.txt": "# All necessary dependencies",
    "README.md": "# Comprehensive documentation with usage instructions"
  },
  "entryPoint": "app.py",
  "title": "Descriptive App Title",
  "description": "Clear explanation of app purpose and functionality"
}

## App Categories & Patterns:

### Data Analysis & Visualization
- Interactive dashboards with multiple chart types
- Data exploration tools with filtering and aggregation
- Statistical analysis with hypothesis testing
- Time series analysis and forecasting

### Business & Productivity Tools
- Financial calculators and modeling tools
- Project management dashboards
- Performance metrics and KPI tracking
- ROI and cost-benefit analysis tools

### Educational & Learning
- Interactive tutorials and demonstrations
- Mathematical concept visualizers
- Scientific simulations and experiments
- Quiz and assessment tools

### Machine Learning & AI
- Model comparison and evaluation tools
- Hyperparameter tuning interfaces
- Prediction and classification demos
- Data preprocessing and feature engineering tools

## Implementation Guidelines:
1. **Start Strong**: Compelling title, clear description, immediate value proposition
2. **Guide Users**: Step-by-step instructions, sample data, helpful examples
3. **Make it Interactive**: Sliders, selectboxes, file uploads, real-time updates
4. **Show Results**: Clear visualizations, formatted outputs, downloadable results
5. **Handle Edge Cases**: Input validation, error messages, fallback options

## Browser-Specific Optimizations:
- Use matplotlib for reliable plotting in all browsers
- Implement efficient data structures for large datasets
- Add progress bars for long-running computations
- Use st.cache_data for expensive operations
- Provide sample data when external sources aren't available

Create applications that users will find genuinely useful and want to share with others.
`;

const streamlitUpdatePrompt = (currentContent: string) => `
Update the following Streamlit application based on the given prompt. Maintain the existing structure and only modify what's necessary.

Current application:
${currentContent}

Return the updated application in the same JSON format with files, entryPoint, title, and description.
`;

export const streamlitDocumentHandler = createDocumentHandler<'streamlit'>({
  kind: 'streamlit',
  onCreateDocument: async ({ title, dataStream }) => {
    let draftContent = '';

    const { fullStream } = streamObject({
      model: myProvider.languageModel('artifact-model'),
      system: streamlitPrompt,
      prompt: title,
      schema: z.object({
        files: z.record(z.string()).describe('Object containing file paths as keys and file contents as values'),
        entryPoint: z.string().describe('Main file to run (usually app.py)'),
        title: z.string().describe('Application title'),
        description: z.string().describe('Brief description of the application'),
      }),
    });

    for await (const delta of fullStream) {
      const { type } = delta;

      if (type === 'object') {
        const { object } = delta;
        
        if (object.files || object.entryPoint || object.title || object.description) {
          const streamlitProject = {
            files: object.files || {},
            entryPoint: object.entryPoint || 'app.py',
            title: object.title || 'Streamlit App',
            description: object.description || 'A Streamlit application',
          };

          draftContent = JSON.stringify(streamlitProject, null, 2);

          dataStream.writeData({
            type: 'streamlit-delta',
            content: draftContent,
          });
        }
      }
    }

    return draftContent;
  },
  onUpdateDocument: async ({ document, description, dataStream }) => {
    let draftContent = '';

    const { fullStream } = streamObject({
      model: myProvider.languageModel('artifact-model'),
      system: streamlitUpdatePrompt(document.content || ''),
      prompt: description,
      schema: z.object({
        files: z.record(z.string()).describe('Object containing file paths as keys and file contents as values'),
        entryPoint: z.string().describe('Main file to run (usually app.py)'),
        title: z.string().describe('Application title'),
        description: z.string().describe('Brief description of the application'),
      }),
      experimental_providerMetadata: {
        openai: {
          prediction: {
            type: 'content',
            content: document.content,
          },
        },
      },
    });

    for await (const delta of fullStream) {
      const { type } = delta;

      if (type === 'object') {
        const { object } = delta;
        
        if (object.files || object.entryPoint || object.title || object.description) {
          const streamlitProject = {
            files: object.files || {},
            entryPoint: object.entryPoint || 'app.py',
            title: object.title || 'Streamlit App',
            description: object.description || 'A Streamlit application',
          };

          draftContent = JSON.stringify(streamlitProject, null, 2);

          dataStream.writeData({
            type: 'streamlit-delta',
            content: draftContent,
          });
        }
      }
    }

    return draftContent;
  },
});
