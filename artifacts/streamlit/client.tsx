import { Artifact } from '@/components/create-artifact';
import { StreamlitEditor } from '@/components/streamlit-editor';
import { PlayIcon, StopIcon, RefreshIcon, CopyIcon } from '@/components/icons';
import { toast } from 'sonner';
import { generateUUID } from '@/lib/utils';
import {
  streamlitClientExecutionManager,
  type StreamlitExecution
} from '@/lib/streamlit/client-execution-manager';

interface StreamlitProject {
  files: Record<string, string>;
  entryPoint: string;
  title: string;
  description: string;
}

interface StreamlitArtifactMetadata {
  execution?: StreamlitExecution;
  isRunning: boolean;
  logs: Array<{
    id: string;
    type: 'stdout' | 'stderr' | 'info';
    message: string;
    timestamp: Date;
  }>;
}

const parseStreamlitProject = (content: string): StreamlitProject | null => {
  try {
    const parsed = JSON.parse(content);
    if (parsed.files && parsed.entryPoint && parsed.title && parsed.description) {
      return parsed as StreamlitProject;
    }
  } catch (error) {
    console.error('Failed to parse Streamlit project:', error);
  }
  return null;
};

export const streamlitArtifact = new Artifact<'streamlit', StreamlitArtifactMetadata>({
  kind: 'streamlit',
  description: 'Useful for creating interactive Streamlit web applications with real-time preview.',
  initialize: async ({ setMetadata }) => {
    setMetadata({
      isRunning: false,
      logs: [],
    });
  },
  onStreamPart: ({ streamPart, setArtifact }) => {
    if (streamPart.type === 'streamlit-delta') {
      setArtifact((draftArtifact) => ({
        ...draftArtifact,
        content: streamPart.content as string,
        isVisible:
          draftArtifact.status === 'streaming' &&
          draftArtifact.content.length > 100 &&
          draftArtifact.content.length < 200
            ? true
            : draftArtifact.isVisible,
        status: 'streaming',
      }));
    }
  },
  content: ({ metadata, setMetadata, ...props }) => {
    return (
      <StreamlitEditor
        {...props}
        metadata={metadata}
        setMetadata={setMetadata}
      />
    );
  },
  actions: [
    {
      icon: <PlayIcon size={18} />,
      label: 'Run',
      description: 'Start Streamlit application',
      onClick: async ({ content, setMetadata }) => {
        const project = parseStreamlitProject(content);
        if (!project) {
          toast.error('Invalid Streamlit project format');
          return;
        }

        const projectId = generateUUID();

        setMetadata((prev) => ({
          ...prev,
          isRunning: true,
          logs: [
            ...prev.logs,
            {
              id: generateUUID(),
              type: 'info',
              message: 'Starting Streamlit application...',
              timestamp: new Date(),
            },
          ],
        }));

        try {
          // Get the preview element from the DOM
          const previewElement = document.getElementById(`streamlit-preview-${projectId}`);
          if (!previewElement) {
            throw new Error('Preview element not found. Please ensure the editor is visible.');
          }

          const execution = await streamlitClientExecutionManager.startStreamlitApp(
            projectId,
            project,
            previewElement as HTMLElement,
            (output) => {
              setMetadata((prev) => ({
                ...prev,
                logs: [
                  ...prev.logs,
                  {
                    id: generateUUID(),
                    type: output.type,
                    message: output.data,
                    timestamp: output.timestamp,
                  },
                ],
              }));
            }
          );

          setMetadata((prev) => ({
            ...prev,
            execution,
            isRunning: execution.status === 'running' || execution.status === 'starting',
          }));

          toast.success('Streamlit app started successfully');
        } catch (error) {
          setMetadata((prev) => ({
            ...prev,
            isRunning: false,
            logs: [
              ...prev.logs,
              {
                id: generateUUID(),
                type: 'stderr',
                message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
                timestamp: new Date(),
              },
            ],
          }));
          toast.error('Failed to start Streamlit app');
        }
      },
      isDisabled: ({ metadata }) => metadata?.isRunning || false,
    },
    {
      icon: <StopIcon size={18} />,
      label: 'Stop',
      description: 'Stop Streamlit application',
      onClick: async ({ metadata, setMetadata }) => {
        if (!metadata?.execution?.projectId) {
          toast.error('No running Streamlit app to stop');
          return;
        }

        try {
          await streamlitClientExecutionManager.stopExecution(metadata.execution.projectId);

          setMetadata((prev) => ({
            ...prev,
            execution: undefined,
            isRunning: false,
            logs: [
              ...prev.logs,
              {
                id: generateUUID(),
                type: 'info',
                message: 'Streamlit app stopped',
                timestamp: new Date(),
              },
            ],
          }));

          toast.success('Streamlit app stopped');
        } catch (error) {
          toast.error('Failed to stop Streamlit app');
        }
      },
      isDisabled: ({ metadata }) => !metadata?.isRunning,
    },
    {
      icon: <RefreshIcon size={18} />,
      label: 'Restart',
      description: 'Restart Streamlit application with current code',
      onClick: async ({ content, metadata, setMetadata }) => {
        const project = parseStreamlitProject(content);
        if (!project || !metadata?.execution?.projectId) {
          toast.error('Cannot restart: invalid project or no running app');
          return;
        }

        setMetadata((prev) => ({
          ...prev,
          logs: [
            ...prev.logs,
            {
              id: generateUUID(),
              type: 'info',
              message: 'Restarting Streamlit application...',
              timestamp: new Date(),
            },
          ],
        }));

        try {
          const execution = await streamlitClientExecutionManager.updateProject(
            metadata.execution.projectId,
            project,
            (output) => {
              setMetadata((prev) => ({
                ...prev,
                logs: [
                  ...prev.logs,
                  {
                    id: generateUUID(),
                    type: output.type,
                    message: output.data,
                    timestamp: output.timestamp,
                  },
                ],
              }));
            }
          );

          setMetadata((prev) => ({
            ...prev,
            execution,
            isRunning: execution.status === 'running' || execution.status === 'starting',
          }));

          toast.success('Streamlit app restarted');
        } catch (error) {
          toast.error('Failed to restart Streamlit app');
        }
      },
      isDisabled: ({ metadata }) => !metadata?.execution,
    },
    {
      icon: <CopyIcon size={18} />,
      label: 'Copy Code',
      description: 'Copy main application code to clipboard',
      onClick: async ({ content }) => {
        const project = parseStreamlitProject(content);
        if (!project) {
          toast.error('Invalid Streamlit project format');
          return;
        }

        const mainCode = project.files[project.entryPoint];
        if (!mainCode) {
          toast.error('Main application file not found');
          return;
        }

        try {
          await navigator.clipboard.writeText(mainCode);
          toast.success('Code copied to clipboard');
        } catch (error) {
          toast.error('Failed to copy code');
        }
      },
      isDisabled: ({ content }) => !parseStreamlitProject(content),
    },
  ],
  toolbar: [],
});
