import { useState, useCallback, useRef, useEffect } from 'react';
import {
  ReactProject,
  SandboxExecution,
  SandboxError,
  SandboxLog,
  ProjectTemplate
} from '../types';
import { ReactSandboxRuntime } from '../core/sandbox-runtime';
import { createProjectFromTemplate, availableTemplates } from '../utils/templates';
import { reactSandboxAPI } from '../api/client';

export function useReactSandbox(options: { useServerAPI?: boolean } = {}) {
  const [project, setProject] = useState<ReactProject | null>(null);
  const [execution, setExecution] = useState<SandboxExecution | null>(null);
  const [logs, setLogs] = useState<SandboxLog[]>([]);
  const [errors, setErrors] = useState<SandboxError[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const runtimeRef = useRef<ReactSandboxRuntime | null>(null);
  const { useServerAPI = false } = options;

  // Initialize runtime
  useEffect(() => {
    if (!runtimeRef.current) {
      runtimeRef.current = new ReactSandboxRuntime({
        enableConsole: true,
        enableNetworking: true,
        maxMemory: 100 * 1024 * 1024 // 100MB
      });

      // Listen to runtime events
      runtimeRef.current.addEventListener((event) => {
        switch (event.type) {
          case 'console-log':
            setLogs(prev => [...prev, event.log]);
            break;
          case 'runtime-error':
          case 'build-error':
            setErrors(prev => [...prev, event.error]);
            break;
          case 'runtime-start':
            setIsRunning(true);
            break;
          case 'build-complete':
            if (!event.result.success) {
              setIsRunning(false);
            }
            break;
        }
      });
    }

    return () => {
      if (runtimeRef.current) {
        runtimeRef.current.cleanup();
      }
    };
  }, []);

  // Create new project from template
  const createProject = useCallback(async (template: ProjectTemplate, name?: string) => {
    if (useServerAPI) {
      setIsLoading(true);
      try {
        const newProject = await reactSandboxAPI.createProject({
          name: name || template.name,
          templateId: template.id,
          description: template.description
        });
        setProject(newProject);
        setLogs([]);
        setErrors([]);
        setExecution(null);
        setIsRunning(false);
        return newProject;
      } catch (error) {
        console.error('Failed to create project on server:', error);
        // Fallback to local creation
      } finally {
        setIsLoading(false);
      }
    }

    // Local creation
    const newProject = createProjectFromTemplate(template, name);
    setProject(newProject);
    setLogs([]);
    setErrors([]);
    setExecution(null);
    setIsRunning(false);
    return newProject;
  }, [useServerAPI]);

  // Update project
  const updateProject = useCallback((updatedProject: ReactProject) => {
    setProject(updatedProject);
  }, []);

  // Update project files
  const updateFile = useCallback(async (path: string, content: string) => {
    if (!project) return;

    if (useServerAPI) {
      setIsLoading(true);
      try {
        const updatedProject = await reactSandboxAPI.updateFile(project.id, path, content);
        setProject(updatedProject);
        return;
      } catch (error) {
        console.error('Failed to update file on server:', error);
        // Fallback to local update
      } finally {
        setIsLoading(false);
      }
    }

    // Local update
    const updatedProject = {
      ...project,
      files: {
        ...project.files,
        [path]: content
      }
    };
    setProject(updatedProject);
  }, [project, useServerAPI]);

  // Add new file
  const addFile = useCallback((path: string, content = '') => {
    if (!project) return;

    const updatedProject = {
      ...project,
      files: {
        ...project.files,
        [path]: content
      }
    };
    setProject(updatedProject);
  }, [project]);

  // Delete file
  const deleteFile = useCallback((path: string) => {
    if (!project) return;

    const { [path]: deleted, ...remainingFiles } = project.files;
    const updatedProject = {
      ...project,
      files: remainingFiles
    };
    setProject(updatedProject);
  }, [project]);

  // Rename file
  const renameFile = useCallback((oldPath: string, newPath: string) => {
    if (!project || !project.files[oldPath]) return;

    const content = project.files[oldPath];
    const { [oldPath]: deleted, ...remainingFiles } = project.files;
    
    const updatedProject = {
      ...project,
      files: {
        ...remainingFiles,
        [newPath]: content
      }
    };
    setProject(updatedProject);
  }, [project]);

  // Install dependency
  const installDependency = useCallback((packageName: string, version = 'latest') => {
    if (!project) return;

    const updatedProject = {
      ...project,
      dependencies: {
        ...project.dependencies,
        [packageName]: version
      }
    };
    setProject(updatedProject);
  }, [project]);

  // Uninstall dependency
  const uninstallDependency = useCallback((packageName: string) => {
    if (!project) return;

    const { [packageName]: removed, ...remainingDeps } = project.dependencies;
    const updatedProject = {
      ...project,
      dependencies: remainingDeps
    };
    setProject(updatedProject);
  }, [project]);

  // Run project
  const runProject = useCallback(async (mountElement: HTMLElement) => {
    if (!project || !runtimeRef.current) return;

    try {
      setIsRunning(true);
      setErrors([]);
      
      const newExecution = await runtimeRef.current.runProject(project, mountElement);
      setExecution(newExecution);
      
      if (newExecution.status === 'error') {
        setIsRunning(false);
      }
      
      return newExecution;
    } catch (error: any) {
      setIsRunning(false);
      const sandboxError: SandboxError = {
        type: 'runtime',
        message: error.message,
        stack: error.stack
      };
      setErrors(prev => [...prev, sandboxError]);
      throw error;
    }
  }, [project]);

  // Stop project
  const stopProject = useCallback(async () => {
    if (!project || !runtimeRef.current) return;

    await runtimeRef.current.stopExecution(project.id);
    setExecution(null);
    setIsRunning(false);
  }, [project]);

  // Clear logs
  const clearLogs = useCallback(() => {
    setLogs([]);
    setErrors([]);
  }, []);

  // Execute command in console
  const executeCommand = useCallback((command: string) => {
    // Add command to logs
    const commandLog: SandboxLog = {
      id: `cmd_${Date.now()}`,
      type: 'log',
      message: `> ${command}`,
      timestamp: new Date(),
      source: 'console'
    };
    setLogs(prev => [...prev, commandLog]);

    // Execute command (simplified evaluation)
    try {
      const result = eval(command);
      const resultLog: SandboxLog = {
        id: `result_${Date.now()}`,
        type: 'log',
        message: String(result),
        timestamp: new Date(),
        source: 'console'
      };
      setLogs(prev => [...prev, resultLog]);
    } catch (error: any) {
      const errorLog: SandboxLog = {
        id: `error_${Date.now()}`,
        type: 'error',
        message: error.message,
        timestamp: new Date(),
        source: 'console'
      };
      setLogs(prev => [...prev, errorLog]);
    }
  }, []);

  // Get project stats
  const getProjectStats = useCallback(() => {
    if (!project) return null;

    const fileCount = Object.keys(project.files).length;
    const dependencyCount = Object.keys(project.dependencies).length;
    const totalLines = Object.values(project.files)
      .reduce((total, content) => total + content.split('\n').length, 0);
    const totalSize = Object.values(project.files)
      .reduce((total, content) => total + content.length, 0);

    return {
      fileCount,
      dependencyCount,
      totalLines,
      totalSize,
      logCount: logs.length,
      errorCount: errors.length
    };
  }, [project, logs, errors]);

  // Export project
  const exportProject = useCallback(() => {
    if (!project) return null;

    return {
      ...project,
      exportedAt: new Date().toISOString(),
      stats: getProjectStats()
    };
  }, [project, getProjectStats]);

  // Import project
  const importProject = useCallback((projectData: any) => {
    try {
      // Validate project structure
      if (!projectData.id || !projectData.files || !projectData.dependencies) {
        throw new Error('Invalid project format');
      }

      setProject(projectData);
      setLogs([]);
      setErrors([]);
      setExecution(null);
      setIsRunning(false);
      
      return projectData;
    } catch (error: any) {
      throw new Error(`Failed to import project: ${error.message}`);
    }
  }, []);

  return {
    // State
    project,
    execution,
    logs,
    errors,
    isRunning,
    
    // Project management
    createProject,
    updateProject,
    exportProject,
    importProject,
    
    // File operations
    updateFile,
    addFile,
    deleteFile,
    renameFile,
    
    // Dependency management
    installDependency,
    uninstallDependency,
    
    // Execution
    runProject,
    stopProject,
    
    // Console
    clearLogs,
    executeCommand,
    
    // Utilities
    getProjectStats,
    availableTemplates
  };
}
