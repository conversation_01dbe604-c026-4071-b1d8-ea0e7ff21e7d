import { ReactProject, ProjectTemplate } from '../types';
import { nanoid } from 'nanoid';

/**
 * Project templates for React Sandbox
 */

export const basicReactTemplate: ProjectTemplate = {
  id: 'basic-react',
  name: 'Basic React App',
  description: 'A simple React application with a counter component',
  files: {
    'App.jsx': `import React, { useState } from 'react';
import './App.css';

function App() {
  const [count, setCount] = useState(0);

  return (
    <div className="App">
      <header className="App-header">
        <h1>Welcome to React Sandbox</h1>
        <div className="counter">
          <p>You clicked {count} times</p>
          <button onClick={() => setCount(count + 1)}>
            Click me
          </button>
          <button onClick={() => setCount(0)}>
            Reset
          </button>
        </div>
      </header>
    </div>
  );
}

export default App;`,

    'App.css': `.App {
  text-align: center;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.counter {
  margin: 20px 0;
}

.counter p {
  font-size: 18px;
  margin-bottom: 10px;
}

.counter button {
  background-color: #61dafb;
  border: none;
  padding: 10px 20px;
  margin: 0 5px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  color: #282c34;
  font-weight: bold;
}

.counter button:hover {
  background-color: #21a1c4;
}

.counter button:active {
  transform: scale(0.98);
}`,

    'index.js': `import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(<App />);`
  },
  dependencies: {
    'react': '^18.0.0',
    'react-dom': '^18.0.0'
  },
  settings: {
    typescript: false,
    jsx: true,
    hotReload: true,
    autoSave: true,
    showConsole: true,
    showInspector: false,
    bundler: 'webpack',
    target: 'es2020'
  },
  preview: 'A simple React app with a counter that demonstrates state management and event handling.'
};

export const typescriptReactTemplate: ProjectTemplate = {
  id: 'typescript-react',
  name: 'TypeScript React App',
  description: 'A React application with TypeScript support',
  files: {
    'App.tsx': `import React, { useState } from 'react';
import './App.css';

interface CounterProps {
  initialCount?: number;
}

const Counter: React.FC<CounterProps> = ({ initialCount = 0 }) => {
  const [count, setCount] = useState<number>(initialCount);

  const increment = (): void => setCount(prev => prev + 1);
  const decrement = (): void => setCount(prev => prev - 1);
  const reset = (): void => setCount(initialCount);

  return (
    <div className="counter">
      <h2>Counter: {count}</h2>
      <div className="buttons">
        <button onClick={decrement}>-</button>
        <button onClick={increment}>+</button>
        <button onClick={reset}>Reset</button>
      </div>
    </div>
  );
};

const App: React.FC = () => {
  return (
    <div className="App">
      <header className="App-header">
        <h1>TypeScript React Sandbox</h1>
        <Counter initialCount={0} />
        <Counter initialCount={10} />
      </header>
    </div>
  );
};

export default App;`,

    'App.css': `.App {
  text-align: center;
}

.App-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  color: white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.counter {
  margin: 20px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  backdrop-filter: blur(10px);
}

.counter h2 {
  margin-bottom: 15px;
  font-size: 24px;
}

.buttons {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.buttons button {
  background: #4CAF50;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  color: white;
  font-weight: bold;
  transition: all 0.3s ease;
}

.buttons button:hover {
  background: #45a049;
  transform: translateY(-2px);
}

.buttons button:active {
  transform: translateY(0);
}`,

    'index.tsx': `import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(<App />);`
  },
  dependencies: {
    'react': '^18.0.0',
    'react-dom': '^18.0.0',
    '@types/react': '^18.0.0',
    '@types/react-dom': '^18.0.0'
  },
  devDependencies: {
    'typescript': '^5.0.0'
  },
  settings: {
    typescript: true,
    jsx: true,
    hotReload: true,
    autoSave: true,
    showConsole: true,
    showInspector: false,
    bundler: 'webpack',
    target: 'es2020'
  },
  preview: 'A TypeScript React app with multiple counter components demonstrating type safety and component props.'
};

export const hooksExampleTemplate: ProjectTemplate = {
  id: 'hooks-example',
  name: 'React Hooks Example',
  description: 'Demonstrates various React hooks in action',
  files: {
    'App.jsx': `import React, { useState, useEffect, useReducer, useContext, createContext } from 'react';
import './App.css';

// Context for theme
const ThemeContext = createContext();

// Reducer for todo list
const todoReducer = (state, action) => {
  switch (action.type) {
    case 'ADD_TODO':
      return [...state, { id: Date.now(), text: action.text, completed: false }];
    case 'TOGGLE_TODO':
      return state.map(todo =>
        todo.id === action.id ? { ...todo, completed: !todo.completed } : todo
      );
    case 'DELETE_TODO':
      return state.filter(todo => todo.id !== action.id);
    default:
      return state;
  }
};

// Timer component using useEffect
const Timer = () => {
  const [seconds, setSeconds] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setSeconds(prev => prev + 1);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="timer">
      <h3>Timer: {seconds}s</h3>
    </div>
  );
};

// Todo component using useReducer
const TodoList = () => {
  const [todos, dispatch] = useReducer(todoReducer, []);
  const [inputValue, setInputValue] = useState('');

  const addTodo = () => {
    if (inputValue.trim()) {
      dispatch({ type: 'ADD_TODO', text: inputValue });
      setInputValue('');
    }
  };

  return (
    <div className="todo-list">
      <h3>Todo List</h3>
      <div className="todo-input">
        <input
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && addTodo()}
          placeholder="Add a todo..."
        />
        <button onClick={addTodo}>Add</button>
      </div>
      <ul>
        {todos.map(todo => (
          <li key={todo.id} className={todo.completed ? 'completed' : ''}>
            <span onClick={() => dispatch({ type: 'TOGGLE_TODO', id: todo.id })}>
              {todo.text}
            </span>
            <button onClick={() => dispatch({ type: 'DELETE_TODO', id: todo.id })}>
              Delete
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
};

// Theme toggle using useContext
const ThemeToggle = () => {
  const { theme, toggleTheme } = useContext(ThemeContext);

  return (
    <button className="theme-toggle" onClick={toggleTheme}>
      Current theme: {theme}
    </button>
  );
};

const App = () => {
  const [theme, setTheme] = useState('light');

  const toggleTheme = () => {
    setTheme(prev => prev === 'light' ? 'dark' : 'light');
  };

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      <div className={\`App \${theme}\`}>
        <header className="App-header">
          <h1>React Hooks Example</h1>
          <ThemeToggle />
          <Timer />
          <TodoList />
        </header>
      </div>
    </ThemeContext.Provider>
  );
};

export default App;`,

    'App.css': `.App {
  text-align: center;
  transition: all 0.3s ease;
}

.App.light {
  background-color: #f5f5f5;
  color: #333;
}

.App.dark {
  background-color: #333;
  color: #f5f5f5;
}

.App-header {
  padding: 20px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
}

.theme-toggle {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
}

.timer {
  padding: 20px;
  background: rgba(0, 123, 255, 0.1);
  border-radius: 10px;
  border: 2px solid #007bff;
}

.todo-list {
  max-width: 400px;
  width: 100%;
}

.todo-input {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.todo-input input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 16px;
}

.todo-input button {
  background: #28a745;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
}

.todo-list ul {
  list-style: none;
  padding: 0;
}

.todo-list li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  margin: 5px 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 5px;
  cursor: pointer;
}

.todo-list li.completed span {
  text-decoration: line-through;
  opacity: 0.6;
}

.todo-list li button {
  background: #dc3545;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
}`,

    'index.js': `import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(<App />);`
  },
  dependencies: {
    'react': '^18.0.0',
    'react-dom': '^18.0.0'
  },
  settings: {
    typescript: false,
    jsx: true,
    hotReload: true,
    autoSave: true,
    showConsole: true,
    showInspector: false,
    bundler: 'webpack',
    target: 'es2020'
  },
  preview: 'A comprehensive example showcasing useState, useEffect, useReducer, useContext, and custom hooks.'
};

export const availableTemplates: ProjectTemplate[] = [
  basicReactTemplate,
  typescriptReactTemplate,
  hooksExampleTemplate
];

/**
 * Create a new React project from a template
 */
export function createProjectFromTemplate(
  template: ProjectTemplate,
  name?: string
): ReactProject {
  const defaultSettings: ProjectSettings = {
    typescript: false,
    jsx: true,
    hotReload: true,
    autoSave: true,
    showConsole: true,
    showInspector: false,
    bundler: 'webpack',
    target: 'es2020'
  };

  return {
    id: nanoid(),
    name: name || template.name,
    description: template.description,
    files: { ...template.files },
    entryPoint: 'index.js',
    dependencies: { ...template.dependencies },
    devDependencies: { ...template.devDependencies },
    template: template.id as any,
    settings: { ...defaultSettings, ...template.settings }
  };
}

/**
 * Get template by ID
 */
export function getTemplateById(id: string): ProjectTemplate | undefined {
  return availableTemplates.find(template => template.id === id);
}
