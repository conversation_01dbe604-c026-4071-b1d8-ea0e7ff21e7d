import React, { createElement } from 'react';
import { createRoot } from 'react-dom/client';
import {
  ReactProject,
  SandboxExecution,
  SandboxError,
  SandboxLog,
  RuntimeContext,
  SandboxConfig
} from '../types';
import { ReactTranspiler } from './transpiler';
import { SandboxPackageManager } from './package-manager';
import { SandboxErrorBoundary } from '../components/error-boundary';
import { performanceMonitor } from '../utils/performance-monitor';

/**
 * React Sandbox Runtime
 * Manages the execution environment for React applications
 */
export class ReactSandboxRuntime {
  private transpiler: ReactTranspiler;
  private packageManager: SandboxPackageManager;
  private executions = new Map<string, SandboxExecution>();
  private listeners = new Set<(event: any) => void>();
  private config: SandboxConfig;

  constructor(config: Partial<SandboxConfig> = {}) {
    this.transpiler = new ReactTranspiler();
    this.packageManager = new SandboxPackageManager();
    this.config = {
      allowedDomains: ['*'],
      maxMemory: 100 * 1024 * 1024, // 100MB
      timeout: 30000, // 30 seconds
      enableNetworking: true,
      enableFileSystem: false,
      enableConsole: true,
      enableDebugger: false,
      restrictedAPIs: ['eval', 'Function'],
      ...config
    };
  }

  /**
   * Run a React project in the sandbox
   */
  async runProject(
    project: ReactProject,
    mountElement: HTMLElement
  ): Promise<SandboxExecution> {
    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    const execution: SandboxExecution = {
      id: executionId,
      projectId: project.id,
      status: 'building',
      mountElement,
      logs: [],
      startTime: new Date()
    };

    this.executions.set(project.id, execution);
    this.emit({ type: 'build-start', projectId: project.id });

    try {
      // Install dependencies
      await this.installDependencies(project);

      // Build the project
      const buildStartTime = performance.now();
      const buildResult = await this.transpiler.transpileProject(project.files, {
        typescript: project.settings.typescript,
        jsx: project.settings.jsx,
        sourceMaps: true,
        development: true
      });

      const buildTime = performance.now() - buildStartTime;
      execution.buildTime = buildTime;

      // Record build metrics
      performanceMonitor.recordBuildMetrics(
        project.id,
        buildTime,
        buildResult.code?.length || 0
      );

      if (!buildResult.success) {
        execution.status = 'error';
        execution.error = buildResult.errors[0];
        performanceMonitor.recordError(project.id, false);
        this.emit({ type: 'build-error', projectId: project.id, error: execution.error });
        return execution;
      }

      this.emit({ type: 'build-complete', projectId: project.id, result: buildResult });

      // Create runtime context
      const context = this.createRuntimeContext(project);

      // Execute the code
      execution.status = 'running';
      this.emit({ type: 'runtime-start', projectId: project.id });

      const renderStartTime = performance.now();
      await this.executeCode(buildResult.code!, context, mountElement, execution);
      const renderTime = performance.now() - renderStartTime;

      // Record render metrics
      performanceMonitor.recordRenderMetrics(project.id, renderTime);

      return execution;
    } catch (error: any) {
      execution.status = 'error';
      execution.error = {
        type: 'runtime',
        message: error.message,
        stack: error.stack
      };

      performanceMonitor.recordError(project.id, false);
      this.emit({ type: 'runtime-error', projectId: project.id, error: execution.error });
      return execution;
    }
  }

  /**
   * Stop execution
   */
  async stopExecution(projectId: string): Promise<void> {
    const execution = this.executions.get(projectId);
    if (!execution) return;

    execution.status = 'stopped';
    
    // Unmount React component
    if (execution.mountElement) {
      const root = createRoot(execution.mountElement);
      root.unmount();
      execution.mountElement.innerHTML = '';
    }

    this.executions.delete(projectId);
  }

  /**
   * Install project dependencies
   */
  private async installDependencies(project: ReactProject): Promise<void> {
    if (Object.keys(project.dependencies).length === 0) return;

    try {
      await this.packageManager.installPackages(project.dependencies);
    } catch (error: any) {
      throw new Error(`Failed to install dependencies: ${error.message}`);
    }
  }

  /**
   * Create runtime context for sandbox
   */
  private createRuntimeContext(project: ReactProject): RuntimeContext {
    const modules = new Map<string, any>();
    
    // Add built-in modules
    modules.set('react', (window as any).React);
    modules.set('react-dom', (window as any).ReactDOM);
    modules.set('react-dom/client', { createRoot });

    // Create sandboxed console
    const sandboxConsole = this.createSandboxConsole(project.id);

    // Create require function
    const requireFn = (name: string) => {
      if (modules.has(name)) {
        return modules.get(name);
      }
      
      // Try package manager
      try {
        return this.packageManager.createRequireFunction()(name);
      } catch (error) {
        throw new Error(`Module '${name}' not found`);
      }
    };

    return {
      React: (window as any).React,
      ReactDOM: (window as any).ReactDOM,
      modules,
      require: requireFn,
      exports: {},
      console: sandboxConsole,
      process: {
        env: { NODE_ENV: 'development' }
      },
      global: {}
    };
  }

  /**
   * Create sandboxed console
   */
  private createSandboxConsole(projectId: string): Console {
    const originalConsole = console;
    
    const sandboxConsole = {
      log: (...args: any[]) => {
        this.addLog(projectId, 'log', args.join(' '), args);
        if (this.config.enableConsole) {
          originalConsole.log(...args);
        }
      },
      warn: (...args: any[]) => {
        this.addLog(projectId, 'warn', args.join(' '), args);
        if (this.config.enableConsole) {
          originalConsole.warn(...args);
        }
      },
      error: (...args: any[]) => {
        this.addLog(projectId, 'error', args.join(' '), args);
        if (this.config.enableConsole) {
          originalConsole.error(...args);
        }
      },
      info: (...args: any[]) => {
        this.addLog(projectId, 'info', args.join(' '), args);
        if (this.config.enableConsole) {
          originalConsole.info(...args);
        }
      },
      debug: (...args: any[]) => {
        this.addLog(projectId, 'debug', args.join(' '), args);
        if (this.config.enableConsole) {
          originalConsole.debug(...args);
        }
      }
    } as Console;

    return sandboxConsole;
  }

  /**
   * Execute transpiled code in sandbox with error boundary
   */
  private async executeCode(
    code: string,
    context: RuntimeContext,
    mountElement: HTMLElement,
    execution: SandboxExecution
  ): Promise<void> {
    try {
      // Create isolated execution environment
      const sandbox = this.createSandbox(context);

      // Execute the code
      const result = sandbox.execute(code);

      // Create root for React rendering
      const root = createRoot(mountElement);

      // Wrap result in error boundary
      const WrappedComponent = () => (
        <SandboxErrorBoundary
          onError={(error: Error, _errorInfo: any) => {
            const sandboxError: SandboxError = {
              type: 'runtime',
              message: error.message,
              stack: error.stack
            };
            execution.error = sandboxError;
            this.addLog(execution.projectId, 'error', error.message);
            performanceMonitor.recordError(execution.projectId, false);
          }}
        >
          {typeof result === 'function' ? React.createElement(result) : result}
        </SandboxErrorBoundary>
      );

      root.render(<WrappedComponent />);

    } catch (error: any) {
      const sandboxError: SandboxError = {
        type: 'runtime',
        message: error.message,
        stack: error.stack
      };

      execution.error = sandboxError;
      performanceMonitor.recordError(execution.projectId, false);
      throw error;
    }
  }

  /**
   * Create isolated sandbox environment
   */
  private createSandbox(context: RuntimeContext) {
    return {
      execute: (code: string) => {
        // Create function with limited scope
        const sandboxFunction = new Function(
          'React',
          'ReactDOM',
          'require',
          'exports',
          'console',
          'process',
          'global',
          `
          "use strict";
          ${code}
          
          // Return the default export or App component
          if (typeof App !== 'undefined') return App;
          if (typeof exports.default !== 'undefined') return exports.default;
          if (typeof module !== 'undefined' && module.exports) return module.exports;
          `
        );

        return sandboxFunction(
          context.React,
          context.ReactDOM,
          context.require,
          context.exports,
          context.console,
          context.process,
          context.global
        );
      }
    };
  }

  /**
   * Add log entry
   */
  private addLog(
    projectId: string,
    type: SandboxLog['type'],
    message: string,
    args?: any[]
  ): void {
    const log: SandboxLog = {
      id: `log_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      type,
      message,
      args,
      timestamp: new Date(),
      source: 'console'
    };

    const execution = this.executions.get(projectId);
    if (execution) {
      execution.logs.push(log);
    }

    this.emit({ type: 'console-log', projectId, log });
  }

  /**
   * Get execution by project ID
   */
  getExecution(projectId: string): SandboxExecution | undefined {
    return this.executions.get(projectId);
  }

  /**
   * Get all executions
   */
  getAllExecutions(): SandboxExecution[] {
    return Array.from(this.executions.values());
  }

  /**
   * Add event listener
   */
  addEventListener(listener: (event: any) => void): void {
    this.listeners.add(listener);
  }

  /**
   * Remove event listener
   */
  removeEventListener(listener: (event: any) => void): void {
    this.listeners.delete(listener);
  }

  /**
   * Emit event
   */
  private emit(event: any): void {
    this.listeners.forEach(listener => listener(event));
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    // Stop all executions
    for (const projectId of this.executions.keys()) {
      this.stopExecution(projectId);
    }

    // Clear package cache
    this.packageManager.clearCache();
    
    // Clear listeners
    this.listeners.clear();
  }
}
