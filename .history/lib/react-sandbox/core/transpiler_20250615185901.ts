import { transform } from '@babel/standalone';
import { TranspileOptions, BuildResult, SandboxError } from '../types';

/**
 * Production-ready code transpiler for React sandbox
 * Handles JSX, TypeScript, and ES6+ transformations with error handling
 */
export class ReactTranspiler {
  private babelConfig: any;
  private cache = new Map<string, { code: string; map?: string }>();

  constructor() {
    this.babelConfig = {
      presets: [
        ['@babel/preset-env', {
          targets: {
            browsers: ['last 2 versions', 'not dead', '> 0.2%'],
            node: '16'
          },
          modules: false,
          useBuiltIns: 'entry',
          corejs: 3
        }],
        ['@babel/preset-react', {
          runtime: 'automatic',
          development: process.env.NODE_ENV === 'development'
        }]
      ],
      plugins: [
        '@babel/plugin-transform-class-properties',
        '@babel/plugin-transform-object-rest-spread',
        '@babel/plugin-transform-runtime'
      ],
      compact: false,
      retainLines: true
    };
  }

  /**
   * Transpile a single file with caching
   */
  async transpileFile(
    filename: string,
    code: string,
    options: TranspileOptions = {}
  ): Promise<{ code: string; map?: string; errors: SandboxError[] }> {
    const errors: SandboxError[] = [];
    const cacheKey = `${filename}:${this.hashCode(code)}:${JSON.stringify(options)}`;

    // Check cache first
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey)!;
      return { ...cached, errors };
    }

    try {
      // Configure Babel based on options
      const config = this.getBabelConfig(filename, options);

      const result = transform(code, {
        ...config,
        filename,
        sourceMaps: options.sourceMaps,
      });

      if (!result.code) {
        throw new Error('Transpilation failed: No output code generated');
      }

      let transpiledCode = result.code;

      // Apply minification if requested
      if (options.minify) {
        transpiledCode = await this.minifyCode(transpiledCode);
      }

      const resultData = {
        code: transpiledCode,
        map: result.map ? JSON.stringify(result.map) : undefined
      };

      // Cache the result
      this.cache.set(cacheKey, resultData);

      return {
        ...resultData,
        errors
      };
    } catch (error: any) {
      const sandboxError: SandboxError = {
        type: 'compile',
        message: error.message,
        stack: error.stack,
        file: filename,
        line: error.loc?.line,
        column: error.loc?.column,
        code: error.code
      };

      errors.push(sandboxError);

      return {
        code: '',
        errors
      };
    }
  }

  /**
   * Transpile multiple files (project build)
   */
  async transpileProject(
    files: Record<string, string>,
    options: TranspileOptions = {}
  ): Promise<BuildResult> {
    const startTime = Date.now();
    const errors: SandboxError[] = [];
    const warnings: SandboxError[] = [];
    const transpiledFiles: Record<string, string> = {};
    const dependencies: Set<string> = new Set();

    for (const [filename, code] of Object.entries(files)) {
      // Skip non-JS/TS files
      if (!this.isTranspilableFile(filename)) {
        transpiledFiles[filename] = code;
        continue;
      }

      const result = await this.transpileFile(filename, code, options);
      
      if (result.errors.length > 0) {
        errors.push(...result.errors);
      } else {
        transpiledFiles[filename] = result.code;
        
        // Extract dependencies from imports
        const fileDeps = this.extractDependencies(code);
        fileDeps.forEach(dep => dependencies.add(dep));
      }
    }

    // Generate bundle if no errors
    let bundleCode = '';
    if (errors.length === 0) {
      bundleCode = this.generateBundle(transpiledFiles, options);
    }

    const buildTime = Date.now() - startTime;

    return {
      success: errors.length === 0,
      code: bundleCode,
      dependencies: Array.from(dependencies),
      errors,
      warnings,
      buildTime
    };
  }

  /**
   * Get Babel configuration based on file and options
   */
  private getBabelConfig(filename: string, options: TranspileOptions) {
    const config = {
      ...this.babelConfig,
      presets: [...this.babelConfig.presets],
      plugins: [...this.babelConfig.plugins]
    };

    // TypeScript support
    if (options.typescript || filename.endsWith('.ts') || filename.endsWith('.tsx')) {
      config.presets.push(['@babel/preset-typescript', {
        isTSX: filename.endsWith('.tsx'),
        allExtensions: true,
        allowNamespaces: true,
        allowDeclareFields: true
      }]);
    }

    // JSX support (already included in base config, but override if needed)
    if (options.jsx || filename.endsWith('.jsx') || filename.endsWith('.tsx')) {
      // Update the existing React preset
      const reactPresetIndex = config.presets.findIndex(preset =>
        Array.isArray(preset) && preset[0] === '@babel/preset-react'
      );

      if (reactPresetIndex !== -1) {
        config.presets[reactPresetIndex] = ['@babel/preset-react', {
          runtime: 'automatic',
          development: process.env.NODE_ENV === 'development',
          throwIfNamespace: false
        }];
      }
    }

    // Target configuration
    if (options.target) {
      const envPresetIndex = config.presets.findIndex(preset =>
        Array.isArray(preset) && preset[0] === '@babel/preset-env'
      );

      if (envPresetIndex !== -1) {
        config.presets[envPresetIndex][1].targets = {
          ...config.presets[envPresetIndex][1].targets,
          esmodules: options.target === 'esnext'
        };
      }
    }

    // Add additional plugins for modern JavaScript features
    if (options.target === 'esnext') {
      config.plugins.push(
        '@babel/plugin-proposal-optional-chaining',
        '@babel/plugin-proposal-nullish-coalescing-operator'
      );
    }

    return config;
  }

  /**
   * Check if file needs transpilation
   */
  private isTranspilableFile(filename: string): boolean {
    const ext = filename.split('.').pop()?.toLowerCase();
    return ['js', 'jsx', 'ts', 'tsx', 'mjs'].includes(ext || '');
  }

  /**
   * Extract import dependencies from code
   */
  private extractDependencies(code: string): string[] {
    const dependencies: string[] = [];
    
    // Match import statements
    const importRegex = /import\s+(?:[\w\s{},*]+\s+from\s+)?['"]([^'"]+)['"]/g;
    let match;
    
    while ((match = importRegex.exec(code)) !== null) {
      const dep = match[1];
      
      // Only include external dependencies (not relative imports)
      if (!dep.startsWith('.') && !dep.startsWith('/')) {
        dependencies.push(dep);
      }
    }

    // Match require statements
    const requireRegex = /require\(['"]([^'"]+)['"]\)/g;
    while ((match = requireRegex.exec(code)) !== null) {
      const dep = match[1];
      
      if (!dep.startsWith('.') && !dep.startsWith('/')) {
        dependencies.push(dep);
      }
    }

    return [...new Set(dependencies)];
  }

  /**
   * Generate bundle from transpiled files
   */
  private generateBundle(
    files: Record<string, string>, 
    options: TranspileOptions
  ): string {
    const modules: string[] = [];
    
    // Create module wrapper for each file
    for (const [filename, code] of Object.entries(files)) {
      if (this.isTranspilableFile(filename)) {
        const moduleCode = this.wrapModule(filename, code);
        modules.push(moduleCode);
      }
    }

    // Create bundle with module system
    return this.createBundleTemplate(modules, options);
  }

  /**
   * Wrap file in module system
   */
  private wrapModule(filename: string, code: string): string {
    return `
// Module: ${filename}
(function(module, exports, require, __filename, __dirname) {
${code}
})`;
  }

  /**
   * Create bundle template with module loader
   */
  private createBundleTemplate(modules: string[], options: TranspileOptions): string {
    return `
(function() {
  'use strict';
  
  // Module cache
  const moduleCache = {};
  
  // Module loader
  function require(name) {
    if (moduleCache[name]) {
      return moduleCache[name].exports;
    }
    
    const module = { exports: {} };
    moduleCache[name] = module;
    
    // Execute module
    if (modules[name]) {
      modules[name](module, module.exports, require, name, '/');
    }
    
    return module.exports;
  }
  
  // Module registry
  const modules = {
    ${modules.map((mod, index) => `'module_${index}': ${mod}`).join(',\n    ')}
  };
  
  // Start application
  if (modules['index.js'] || modules['App.js'] || modules['main.js']) {
    const entryPoint = modules['index.js'] || modules['App.js'] || modules['main.js'];
    entryPoint();
  }
})();`;
  }

  /**
   * Minify code using Terser
   */
  private async minifyCode(code: string): Promise<string> {
    try {
      const { minify } = await import('terser');
      const result = await minify(code, {
        compress: {
          drop_console: false,
          drop_debugger: true,
          pure_funcs: ['console.log']
        },
        mangle: {
          keep_fnames: true
        },
        format: {
          comments: false
        }
      });

      return result.code || code;
    } catch (error) {
      console.warn('Minification failed, returning original code:', error);
      return code;
    }
  }

  /**
   * Generate hash code for caching
   */
  private hashCode(str: string): string {
    let hash = 0;
    if (str.length === 0) return hash.toString();

    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    return Math.abs(hash).toString(36);
  }

  /**
   * Clear transpilation cache
   */
  public clearCache(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  public getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}
