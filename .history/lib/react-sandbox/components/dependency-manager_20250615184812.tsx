'use client';

import React, { useState, useCallback } from 'react';
import { 
  Package, 
  Plus, 
  Trash2, 
  Download, 
  Search,
  ExternalLink,
  Loader2,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { DependencyManagerProps, PackageInfo } from '../types';
import { SandboxPackageManager } from '../core/package-manager';

export function DependencyManager({
  dependencies,
  onInstall,
  onUninstall,
  onUpdate,
  className
}: DependencyManagerProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<PackageInfo[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isInstalling, setIsInstalling] = useState<string | null>(null);
  const [packageManager] = useState(() => new SandboxPackageManager());

  const handleSearch = useCallback(async () => {
    if (!searchQuery.trim()) return;

    setIsSearching(true);
    try {
      const results = await packageManager.searchPackages(searchQuery, 10);
      setSearchResults(results);
    } catch (error) {
      console.error('Search failed:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, [searchQuery, packageManager]);

  const handleInstall = useCallback(async (packageName: string, version?: string) => {
    setIsInstalling(packageName);
    try {
      await onInstall?.(packageName, version);
    } catch (error) {
      console.error('Install failed:', error);
    } finally {
      setIsInstalling(null);
    }
  }, [onInstall]);

  const handleUninstall = useCallback(async (packageName: string) => {
    await onUninstall?.(packageName);
  }, [onUninstall]);

  const isInstalled = useCallback((packageName: string) => {
    return packageName in dependencies;
  }, [dependencies]);

  const getPackageVersion = useCallback((packageName: string) => {
    return dependencies[packageName] || 'latest';
  }, [dependencies]);

  const formatPackageSize = (size?: number) => {
    if (!size) return 'Unknown';
    if (size < 1024) return `${size}B`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`;
    return `${(size / 1024 / 1024).toFixed(1)}MB`;
  };

  return (
    <div className={cn('flex flex-col h-full', className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b">
        <div className="flex items-center gap-2">
          <Package className="h-4 w-4" />
          <h3 className="text-sm font-medium">Dependencies</h3>
          <Badge variant="outline" className="text-xs">
            {Object.keys(dependencies).length}
          </Badge>
        </div>

        <Dialog>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm" className="h-7">
              <Plus className="h-3 w-3 mr-1" />
              Add
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add Dependency</DialogTitle>
            </DialogHeader>
            
            <div className="space-y-4">
              {/* Search */}
              <div className="flex gap-2">
                <Input
                  placeholder="Search npm packages..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
                <Button 
                  onClick={handleSearch}
                  disabled={isSearching || !searchQuery.trim()}
                >
                  {isSearching ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Search className="h-4 w-4" />
                  )}
                </Button>
              </div>

              {/* Search Results */}
              <ScrollArea className="h-96">
                <div className="space-y-2">
                  {searchResults.map(pkg => (
                    <Card key={pkg.name} className="p-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium text-sm">{pkg.name}</h4>
                            <Badge variant="outline" className="text-xs">
                              v{pkg.version}
                            </Badge>
                            {isInstalled(pkg.name) && (
                              <Badge variant="default" className="text-xs">
                                <CheckCircle className="h-2 w-2 mr-1" />
                                Installed
                              </Badge>
                            )}
                          </div>
                          
                          {pkg.description && (
                            <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                              {pkg.description}
                            </p>
                          )}

                          <div className="flex items-center gap-3 mt-2 text-xs text-muted-foreground">
                            <span>Latest: {pkg.version}</span>
                            <a 
                              href={`https://npmjs.com/package/${pkg.name}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center gap-1 hover:text-foreground"
                            >
                              <ExternalLink className="h-3 w-3" />
                              npm
                            </a>
                          </div>
                        </div>

                        <div className="flex items-center gap-1 ml-3">
                          {isInstalled(pkg.name) ? (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleUninstall(pkg.name)}
                              className="h-7"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          ) : (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleInstall(pkg.name, pkg.version)}
                              disabled={isInstalling === pkg.name}
                              className="h-7"
                            >
                              {isInstalling === pkg.name ? (
                                <Loader2 className="h-3 w-3 animate-spin" />
                              ) : (
                                <Download className="h-3 w-3" />
                              )}
                            </Button>
                          )}
                        </div>
                      </div>
                    </Card>
                  ))}

                  {searchResults.length === 0 && searchQuery && !isSearching && (
                    <div className="text-center py-8 text-muted-foreground">
                      <Package className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No packages found</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Installed Dependencies */}
      <ScrollArea className="flex-1">
        <div className="p-3 space-y-2">
          {Object.keys(dependencies).length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Package className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No dependencies installed</p>
              <p className="text-xs mt-1">Click "Add" to install packages</p>
            </div>
          ) : (
            Object.entries(dependencies).map(([name, version]) => (
              <Card key={name} className="p-3">
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium text-sm truncate">{name}</h4>
                      <Badge variant="outline" className="text-xs">
                        {version}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center gap-3 mt-1 text-xs text-muted-foreground">
                      <a 
                        href={`https://npmjs.com/package/${name}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-1 hover:text-foreground"
                      >
                        <ExternalLink className="h-3 w-3" />
                        View on npm
                      </a>
                    </div>
                  </div>

                  <div className="flex items-center gap-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onUpdate?.(name, 'latest')}
                      className="h-7"
                      title="Update to latest"
                    >
                      <Download className="h-3 w-3" />
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleUninstall(name)}
                      className="h-7"
                      title="Uninstall"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </Card>
            ))
          )}
        </div>
      </ScrollArea>

      {/* Footer */}
      <div className="p-3 border-t bg-muted/30">
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span>{Object.keys(dependencies).length} packages installed</span>
          <span>Powered by npm registry</span>
        </div>
      </div>
    </div>
  );
}
