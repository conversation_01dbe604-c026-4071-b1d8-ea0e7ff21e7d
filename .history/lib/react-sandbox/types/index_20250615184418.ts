// React Sandbox Types

export interface ReactProject {
  id: string;
  name: string;
  description?: string;
  files: Record<string, string>;
  entryPoint: string;
  dependencies: Record<string, string>;
  devDependencies?: Record<string, string>;
  template?: 'basic' | 'typescript' | 'vite' | 'next';
  settings: ProjectSettings;
}

export interface ProjectSettings {
  typescript: boolean;
  jsx: boolean;
  hotReload: boolean;
  autoSave: boolean;
  showConsole: boolean;
  showInspector: boolean;
  bundler: 'webpack' | 'vite' | 'esbuild';
  target: 'es2015' | 'es2017' | 'es2018' | 'es2020' | 'esnext';
}

export interface SandboxExecution {
  id: string;
  projectId: string;
  status: 'idle' | 'building' | 'running' | 'error' | 'stopped';
  mountElement?: HTMLElement;
  error?: SandboxError;
  logs: SandboxLog[];
  startTime?: Date;
  buildTime?: number;
  memoryUsage?: number;
}

export interface SandboxError {
  type: 'compile' | 'runtime' | 'dependency' | 'network';
  message: string;
  stack?: string;
  file?: string;
  line?: number;
  column?: number;
  code?: string;
}

export interface SandboxLog {
  id: string;
  type: 'log' | 'warn' | 'error' | 'info' | 'debug';
  message: string;
  args?: any[];
  timestamp: Date;
  source?: 'console' | 'system' | 'build';
}

export interface ModuleResolution {
  name: string;
  version: string;
  url: string;
  dependencies?: Record<string, string>;
  exports?: Record<string, string>;
}

export interface BuildResult {
  success: boolean;
  code?: string;
  sourceMap?: string;
  dependencies: string[];
  errors: SandboxError[];
  warnings: SandboxError[];
  buildTime: number;
}

export interface RuntimeContext {
  React: any;
  ReactDOM: any;
  modules: Map<string, any>;
  require: (name: string) => any;
  exports: any;
  console: Console;
  process: any;
  global: any;
}

export interface PackageInfo {
  name: string;
  version: string;
  description?: string;
  main?: string;
  module?: string;
  exports?: any;
  dependencies?: Record<string, string>;
  peerDependencies?: Record<string, string>;
}

export interface TranspileOptions {
  typescript?: boolean;
  jsx?: boolean;
  target?: string;
  sourceMaps?: boolean;
  minify?: boolean;
  externals?: string[];
}

export interface HotReloadUpdate {
  type: 'full' | 'partial';
  files: string[];
  timestamp: Date;
}

export interface DevServerOptions {
  port?: number;
  host?: string;
  https?: boolean;
  proxy?: Record<string, string>;
  headers?: Record<string, string>;
}

export interface SandboxConfig {
  allowedDomains: string[];
  maxMemory: number;
  timeout: number;
  enableNetworking: boolean;
  enableFileSystem: boolean;
  enableConsole: boolean;
  enableDebugger: boolean;
  restrictedAPIs: string[];
}

// Event types
export type SandboxEvent = 
  | { type: 'build-start'; projectId: string }
  | { type: 'build-complete'; projectId: string; result: BuildResult }
  | { type: 'build-error'; projectId: string; error: SandboxError }
  | { type: 'runtime-start'; projectId: string }
  | { type: 'runtime-error'; projectId: string; error: SandboxError }
  | { type: 'hot-reload'; projectId: string; update: HotReloadUpdate }
  | { type: 'console-log'; projectId: string; log: SandboxLog }
  | { type: 'dependency-install'; projectId: string; package: string }
  | { type: 'dependency-error'; projectId: string; package: string; error: string };

// Component props
export interface ReactPreviewProps {
  project: ReactProject;
  className?: string;
  onError?: (error: SandboxError) => void;
  onLog?: (log: SandboxLog) => void;
  onStatusChange?: (status: SandboxExecution['status']) => void;
}

export interface CodeEditorProps {
  file: string;
  content: string;
  language?: string;
  onChange?: (content: string) => void;
  onSave?: (content: string) => void;
  readOnly?: boolean;
  className?: string;
}

export interface ConsoleProps {
  logs: SandboxLog[];
  onClear?: () => void;
  onCommand?: (command: string) => void;
  className?: string;
}

export interface FileExplorerProps {
  project: ReactProject;
  selectedFile?: string;
  onFileSelect?: (file: string) => void;
  onFileCreate?: (path: string, content: string) => void;
  onFileDelete?: (path: string) => void;
  onFileRename?: (oldPath: string, newPath: string) => void;
  className?: string;
}

export interface DependencyManagerProps {
  dependencies: Record<string, string>;
  onInstall?: (packageName: string, version?: string) => void;
  onUninstall?: (packageName: string) => void;
  onUpdate?: (packageName: string, version: string) => void;
  className?: string;
}

// Template definitions
export interface ProjectTemplate {
  id: string;
  name: string;
  description: string;
  files: Record<string, string>;
  dependencies: Record<string, string>;
  devDependencies?: Record<string, string>;
  settings: Partial<ProjectSettings>;
  preview?: string;
}

// CDN and package resolution
export interface CDNProvider {
  name: string;
  baseUrl: string;
  resolvePackage: (name: string, version?: string) => Promise<ModuleResolution>;
  getPackageInfo: (name: string) => Promise<PackageInfo>;
}

// Performance monitoring
export interface PerformanceMetrics {
  buildTime: number;
  bundleSize: number;
  memoryUsage: number;
  renderTime: number;
  errorCount: number;
  warningCount: number;
}

// Plugin system
export interface SandboxPlugin {
  name: string;
  version: string;
  hooks: {
    beforeBuild?: (project: ReactProject) => Promise<void>;
    afterBuild?: (project: ReactProject, result: BuildResult) => Promise<void>;
    beforeRun?: (project: ReactProject) => Promise<void>;
    afterRun?: (project: ReactProject, execution: SandboxExecution) => Promise<void>;
    onError?: (error: SandboxError) => Promise<void>;
    onLog?: (log: SandboxLog) => Promise<void>;
  };
}
