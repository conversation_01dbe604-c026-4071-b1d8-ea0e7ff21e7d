import { NextRequest, NextResponse } from 'next/server';

/**
 * GET /api/react-sandbox/health
 * Health check endpoint for React Sandbox API
 */
export async function GET(request: NextRequest) {
  try {
    const startTime = Date.now();
    
    // Check various system components
    const checks = {
      api: true,
      transpiler: false,
      packageManager: false,
      externalServices: false
    };

    // Test transpiler
    try {
      const { ReactTranspiler } = await import('@/lib/react-sandbox/core/transpiler');
      const transpiler = new ReactTranspiler();
      const result = await transpiler.transpileFile(
        'test.jsx',
        'const test = () => <div>Hello</div>;',
        { jsx: true }
      );
      checks.transpiler = result.errors.length === 0;
    } catch (error) {
      console.error('Transpiler health check failed:', error);
    }

    // Test package manager
    try {
      const { SandboxPackageManager } = await import('@/lib/react-sandbox/core/package-manager');
      const packageManager = new SandboxPackageManager();
      checks.packageManager = true; // Just check if it can be instantiated
    } catch (error) {
      console.error('Package manager health check failed:', error);
    }

    // Test external services
    try {
      // Test npm registry
      const npmResponse = await fetch('https://registry.npmjs.org/react', {
        method: 'HEAD',
        signal: AbortSignal.timeout(5000) // 5 second timeout
      });
      
      // Test CDN services
      const esmResponse = await fetch('https://esm.sh/react', {
        method: 'HEAD',
        signal: AbortSignal.timeout(5000)
      });

      checks.externalServices = npmResponse.ok && esmResponse.ok;
    } catch (error) {
      console.error('External services health check failed:', error);
    }

    const responseTime = Date.now() - startTime;
    const allHealthy = Object.values(checks).every(check => check);

    const healthData = {
      status: allHealthy ? 'healthy' : 'degraded',
      timestamp: new Date().toISOString(),
      responseTime,
      version: '1.0.0',
      checks,
      system: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        uptime: process.uptime(),
        memory: {
          used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
          external: Math.round(process.memoryUsage().external / 1024 / 1024)
        }
      },
      features: {
        transpilation: checks.transpiler,
        packageResolution: checks.packageManager,
        externalConnectivity: checks.externalServices,
        errorBoundaries: true,
        performanceMonitoring: true,
        securitySandbox: true
      }
    };

    return NextResponse.json(healthData, {
      status: allHealthy ? 200 : 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      checks: {
        api: false,
        transpiler: false,
        packageManager: false,
        externalServices: false
      }
    }, { 
      status: 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  }
}

/**
 * POST /api/react-sandbox/health
 * Detailed health check with custom tests
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { tests = [] } = body;

    const results = {
      timestamp: new Date().toISOString(),
      requestedTests: tests,
      results: {} as Record<string, any>
    };

    // Run custom tests
    for (const test of tests) {
      try {
        switch (test) {
          case 'transpile-jsx':
            const { ReactTranspiler } = await import('@/lib/react-sandbox/core/transpiler');
            const transpiler = new ReactTranspiler();
            const jsxResult = await transpiler.transpileFile(
              'test.jsx',
              'const App = () => <div><h1>Hello World</h1><p>React is working!</p></div>;',
              { jsx: true, sourceMaps: true }
            );
            results.results[test] = {
              success: jsxResult.errors.length === 0,
              errors: jsxResult.errors,
              hasSourceMap: !!jsxResult.map,
              codeLength: jsxResult.code.length
            };
            break;

          case 'transpile-typescript':
            const { ReactTranspiler: TSTranspiler } = await import('@/lib/react-sandbox/core/transpiler');
            const tsTranspiler = new TSTranspiler();
            const tsResult = await tsTranspiler.transpileFile(
              'test.tsx',
              `interface Props { name: string; }
               const App: React.FC<Props> = ({ name }) => <div>Hello {name}</div>;`,
              { typescript: true, jsx: true }
            );
            results.results[test] = {
              success: tsResult.errors.length === 0,
              errors: tsResult.errors,
              codeLength: tsResult.code.length
            };
            break;

          case 'package-search':
            const searchResponse = await fetch(
              'https://registry.npmjs.org/-/v1/search?text=react&size=1',
              { signal: AbortSignal.timeout(10000) }
            );
            const searchData = await searchResponse.json();
            results.results[test] = {
              success: searchResponse.ok && searchData.objects?.length > 0,
              packageFound: searchData.objects?.[0]?.package?.name === 'react'
            };
            break;

          case 'cdn-resolve':
            const cdnTests = [
              'https://esm.sh/react@18',
              'https://cdn.skypack.dev/react@18',
              'https://unpkg.com/react@18/package.json'
            ];
            
            const cdnResults = await Promise.allSettled(
              cdnTests.map(url => 
                fetch(url, { 
                  method: 'HEAD', 
                  signal: AbortSignal.timeout(5000) 
                })
              )
            );

            results.results[test] = {
              success: cdnResults.some(result => 
                result.status === 'fulfilled' && result.value.ok
              ),
              providers: cdnTests.map((url, index) => ({
                url,
                status: cdnResults[index].status,
                ok: cdnResults[index].status === 'fulfilled' && 
                    (cdnResults[index] as PromiseFulfilledResult<Response>).value.ok
              }))
            };
            break;

          case 'memory-usage':
            const memUsage = process.memoryUsage();
            results.results[test] = {
              success: true,
              heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
              heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
              external: Math.round(memUsage.external / 1024 / 1024),
              rss: Math.round(memUsage.rss / 1024 / 1024),
              healthy: memUsage.heapUsed < 500 * 1024 * 1024 // Less than 500MB
            };
            break;

          default:
            results.results[test] = {
              success: false,
              error: 'Unknown test type'
            };
        }
      } catch (error) {
        results.results[test] = {
          success: false,
          error: error instanceof Error ? error.message : 'Test failed'
        };
      }
    }

    const allSuccessful = Object.values(results.results).every(
      (result: any) => result.success
    );

    return NextResponse.json({
      ...results,
      overallStatus: allSuccessful ? 'healthy' : 'degraded'
    }, {
      status: allSuccessful ? 200 : 207 // 207 Multi-Status for partial success
    });

  } catch (error) {
    console.error('Detailed health check failed:', error);
    
    return NextResponse.json({
      timestamp: new Date().toISOString(),
      overallStatus: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
