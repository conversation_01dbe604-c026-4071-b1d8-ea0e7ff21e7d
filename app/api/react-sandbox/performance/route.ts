import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

const PerformanceMetricSchema = z.object({
  projectId: z.string(),
  type: z.enum(['build', 'render', 'memory', 'error', 'warning']),
  value: z.number(),
  timestamp: z.string().optional(),
  metadata: z.record(z.any()).optional()
});

const PerformanceQuerySchema = z.object({
  projectId: z.string().optional(),
  type: z.enum(['build', 'render', 'memory', 'error', 'warning']).optional(),
  startTime: z.string().optional(),
  endTime: z.string().optional(),
  limit: z.number().min(1).max(1000).default(100)
});

// In-memory storage for performance metrics
// In production, use a time-series database like InfluxDB or TimescaleDB
interface PerformanceMetric {
  id: string;
  projectId: string;
  type: 'build' | 'render' | 'memory' | 'error' | 'warning';
  value: number;
  timestamp: Date;
  metadata?: Record<string, any>;
}

const performanceMetrics: PerformanceMetric[] = [];

/**
 * POST /api/react-sandbox/performance
 * Record performance metrics
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Handle single metric or array of metrics
    const metrics = Array.isArray(body) ? body : [body];
    const validatedMetrics = metrics.map(metric => PerformanceMetricSchema.parse(metric));

    const savedMetrics: PerformanceMetric[] = [];

    for (const metric of validatedMetrics) {
      const performanceMetric: PerformanceMetric = {
        id: `metric_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        projectId: metric.projectId,
        type: metric.type,
        value: metric.value,
        timestamp: metric.timestamp ? new Date(metric.timestamp) : new Date(),
        metadata: metric.metadata
      };

      performanceMetrics.push(performanceMetric);
      savedMetrics.push(performanceMetric);
    }

    // Keep only last 10000 metrics to prevent memory issues
    if (performanceMetrics.length > 10000) {
      performanceMetrics.splice(0, performanceMetrics.length - 10000);
    }

    return NextResponse.json({
      success: true,
      data: savedMetrics,
      message: `Recorded ${savedMetrics.length} performance metric(s)`
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid metric data', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error recording performance metrics:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to record performance metrics' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/react-sandbox/performance
 * Query performance metrics
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'query';

    if (action === 'summary') {
      // Get performance summary
      const projectId = searchParams.get('projectId');
      
      if (!projectId) {
        return NextResponse.json(
          { success: false, error: 'Project ID is required for summary' },
          { status: 400 }
        );
      }

      const projectMetrics = performanceMetrics.filter(m => m.projectId === projectId);
      
      if (projectMetrics.length === 0) {
        return NextResponse.json({
          success: true,
          data: {
            projectId,
            summary: {
              buildTime: { avg: 0, min: 0, max: 0, count: 0 },
              renderTime: { avg: 0, min: 0, max: 0, count: 0 },
              memoryUsage: { avg: 0, min: 0, max: 0, count: 0 },
              errorCount: 0,
              warningCount: 0
            }
          }
        });
      }

      const buildMetrics = projectMetrics.filter(m => m.type === 'build').map(m => m.value);
      const renderMetrics = projectMetrics.filter(m => m.type === 'render').map(m => m.value);
      const memoryMetrics = projectMetrics.filter(m => m.type === 'memory').map(m => m.value);
      const errorCount = projectMetrics.filter(m => m.type === 'error').length;
      const warningCount = projectMetrics.filter(m => m.type === 'warning').length;

      const calculateStats = (values: number[]) => {
        if (values.length === 0) return { avg: 0, min: 0, max: 0, count: 0 };
        return {
          avg: values.reduce((a, b) => a + b, 0) / values.length,
          min: Math.min(...values),
          max: Math.max(...values),
          count: values.length
        };
      };

      return NextResponse.json({
        success: true,
        data: {
          projectId,
          summary: {
            buildTime: calculateStats(buildMetrics),
            renderTime: calculateStats(renderMetrics),
            memoryUsage: calculateStats(memoryMetrics),
            errorCount,
            warningCount
          },
          lastUpdated: new Date().toISOString()
        }
      });

    } else if (action === 'query') {
      // Query metrics with filters
      const query = {
        projectId: searchParams.get('projectId') || undefined,
        type: searchParams.get('type') as any || undefined,
        startTime: searchParams.get('startTime') || undefined,
        endTime: searchParams.get('endTime') || undefined,
        limit: parseInt(searchParams.get('limit') || '100')
      };

      const validatedQuery = PerformanceQuerySchema.parse(query);

      let filteredMetrics = [...performanceMetrics];

      // Apply filters
      if (validatedQuery.projectId) {
        filteredMetrics = filteredMetrics.filter(m => m.projectId === validatedQuery.projectId);
      }

      if (validatedQuery.type) {
        filteredMetrics = filteredMetrics.filter(m => m.type === validatedQuery.type);
      }

      if (validatedQuery.startTime) {
        const startDate = new Date(validatedQuery.startTime);
        filteredMetrics = filteredMetrics.filter(m => m.timestamp >= startDate);
      }

      if (validatedQuery.endTime) {
        const endDate = new Date(validatedQuery.endTime);
        filteredMetrics = filteredMetrics.filter(m => m.timestamp <= endDate);
      }

      // Sort by timestamp (newest first) and limit
      filteredMetrics.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
      filteredMetrics = filteredMetrics.slice(0, validatedQuery.limit);

      return NextResponse.json({
        success: true,
        data: {
          metrics: filteredMetrics,
          total: filteredMetrics.length,
          query: validatedQuery
        }
      });

    } else if (action === 'stats') {
      // Get overall statistics
      const totalMetrics = performanceMetrics.length;
      const projectCount = new Set(performanceMetrics.map(m => m.projectId)).size;
      const typeDistribution = performanceMetrics.reduce((acc, metric) => {
        acc[metric.type] = (acc[metric.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const oldestMetric = performanceMetrics.length > 0 
        ? Math.min(...performanceMetrics.map(m => m.timestamp.getTime()))
        : null;
      
      const newestMetric = performanceMetrics.length > 0
        ? Math.max(...performanceMetrics.map(m => m.timestamp.getTime()))
        : null;

      return NextResponse.json({
        success: true,
        data: {
          totalMetrics,
          projectCount,
          typeDistribution,
          timeRange: {
            oldest: oldestMetric ? new Date(oldestMetric).toISOString() : null,
            newest: newestMetric ? new Date(newestMetric).toISOString() : null
          }
        }
      });

    } else {
      return NextResponse.json(
        { success: false, error: 'Invalid action. Use "query", "summary", or "stats"' },
        { status: 400 }
      );
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error querying performance metrics:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to query performance metrics' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/react-sandbox/performance
 * Clear performance metrics
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');

    if (projectId) {
      // Clear metrics for specific project
      const initialLength = performanceMetrics.length;
      for (let i = performanceMetrics.length - 1; i >= 0; i--) {
        if (performanceMetrics[i].projectId === projectId) {
          performanceMetrics.splice(i, 1);
        }
      }
      const removedCount = initialLength - performanceMetrics.length;

      return NextResponse.json({
        success: true,
        message: `Cleared ${removedCount} metrics for project ${projectId}`
      });
    } else {
      // Clear all metrics
      const clearedCount = performanceMetrics.length;
      performanceMetrics.length = 0;

      return NextResponse.json({
        success: true,
        message: `Cleared all ${clearedCount} performance metrics`
      });
    }

  } catch (error) {
    console.error('Error clearing performance metrics:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to clear performance metrics' },
      { status: 500 }
    );
  }
}
