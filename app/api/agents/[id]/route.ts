import { NextRequest, NextResponse } from 'next/server';
import { dbOrchestration } from '@/lib/microvm';
import { auth } from '@/auth';

export const dynamic = 'force-dynamic';

// GET /api/agents/[id] - Get details of a specific agent
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  // Get the user's session
  const session = await auth();
  if (!session?.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const agentId = params.id;
    const agent = await dbOrchestration.agentManager.getAgent(agentId);
    
    if (!agent) {
      return NextResponse.json({ error: 'Agent not found' }, { status: 404 });
    }
    
    // Ensure the agent belongs to the user
    if (agent.userId !== session.user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }
    
    // Get the associated VM if one exists
    let vm = null;
    if (agent.microVMId) {
      vm = await dbOrchestration.vmManager.getVM(agent.microVMId);
    }
    
    return NextResponse.json({
      agent: {
        ...agent,
        vm: vm ? {
          id: vm.id,
          status: vm.status,
          name: vm.name,
        } : null,
      },
    });
  } catch (error: any) {
    console.error('Error fetching agent:', error);
    return NextResponse.json(
      { error: `Failed to fetch agent: ${error.message}` },
      { status: 500 }
    );
  }
}

// DELETE /api/agents/[id] - Delete an agent and its VM
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  // Get the user's session
  const session = await auth();
  if (!session?.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const agentId = params.id;
    const agent = await dbOrchestration.agentManager.getAgent(agentId);
    
    if (!agent) {
      return NextResponse.json({ error: 'Agent not found' }, { status: 404 });
    }
    
    // Ensure the agent belongs to the user
    if (agent.userId !== session.user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }
    
    await dbOrchestration.deleteAgentWithEnvironment(agentId);
    
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error deleting agent:', error);
    return NextResponse.json(
      { error: `Failed to delete agent: ${error.message}` },
      { status: 500 }
    );
  }
}

// PATCH /api/agents/[id] - Update an agent
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  // Get the user's session
  const session = await auth();
  if (!session?.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const agentId = params.id;
    const agent = await dbOrchestration.agentManager.getAgent(agentId);
    
    if (!agent) {
      return NextResponse.json({ error: 'Agent not found' }, { status: 404 });
    }
    
    // Ensure the agent belongs to the user
    if (agent.userId !== session.user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }
    
    const body = await req.json();
    const { name, role, description, systemPrompt, tools } = body;
    
    // Update the agent
    const updatedAgent = await dbOrchestration.agentManager.updateAgent(agentId, {
      name,
      role,
      description,
      systemPrompt,
      tools,
    });
    
    // Get the associated VM if one exists
    let vm = null;
    if (updatedAgent.microVMId) {
      vm = await dbOrchestration.vmManager.getVM(updatedAgent.microVMId);
    }
    
    return NextResponse.json({
      agent: {
        ...updatedAgent,
        vm: vm ? {
          id: vm.id,
          status: vm.status,
          name: vm.name,
        } : null,
      },
    });
  } catch (error: any) {
    console.error('Error updating agent:', error);
    return NextResponse.json(
      { error: `Failed to update agent: ${error.message}` },
      { status: 500 }
    );
  }
} 