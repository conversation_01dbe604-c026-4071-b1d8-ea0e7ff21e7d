import { NextRequest, NextResponse } from 'next/server';
import { dbOrchestration } from '@/lib/microvm';
import { auth } from '@/auth';

export const dynamic = 'force-dynamic';

// POST /api/agents/[id]/vm - Handles both VM control and command execution
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  // Check the request path to distinguish between endpoints
  if (req.nextUrl.pathname.endsWith('/command')) {
    return executeCommand(req, params);
  }
  
  // If not a command execution request, process as regular VM control
  return vmControl(req, params);
}

// Helper function for VM control
async function vmControl(
  req: NextRequest,
  params: { id: string }
) {
  // Get the user's session
  const session = await auth();
  if (!session?.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const agentId = params.id;
    const agent = await dbOrchestration.agentManager.getAgent(agentId);
    
    if (!agent) {
      return NextResponse.json({ error: 'Agent not found' }, { status: 404 });
    }
    
    // Ensure the agent belongs to the user
    if (agent.userId !== session.user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Get the action from the request body
    const { action } = await req.json();
    
    if (!agent.microVMId) {
      return NextResponse.json(
        { error: 'Agent does not have an associated VM' },
        { status: 400 }
      );
    }
    
    if (action === 'start') {
      const vm = await dbOrchestration.startAgentVM(agentId);
      return NextResponse.json({ vm });
    } else if (action === 'stop') {
      const vm = await dbOrchestration.stopAgentVM(agentId);
      return NextResponse.json({ vm });
    } else {
      return NextResponse.json(
        { error: 'Invalid action. Use "start" or "stop"' },
        { status: 400 }
      );
    }
  } catch (error: any) {
    console.error('Error controlling VM:', error);
    return NextResponse.json(
      { error: `Failed to control VM: ${error.message}` },
      { status: 500 }
    );
  }
}

// Helper function for command execution
async function executeCommand(
  req: NextRequest,
  params: { id: string }
) {
  // Get the user's session
  const session = await auth();
  if (!session?.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const agentId = params.id;
    const agent = await dbOrchestration.agentManager.getAgent(agentId);
    
    if (!agent) {
      return NextResponse.json({ error: 'Agent not found' }, { status: 404 });
    }
    
    // Ensure the agent belongs to the user
    if (agent.userId !== session.user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Get the command from the request body
    const { command } = await req.json();
    
    if (!command) {
      return NextResponse.json(
        { error: 'Command is required' },
        { status: 400 }
      );
    }
    
    if (!agent.microVMId) {
      return NextResponse.json(
        { error: 'Agent does not have an associated VM' },
        { status: 400 }
      );
    }
    
    const result = await dbOrchestration.executeAgentCommand(agentId, command);
    return NextResponse.json({ result });
  } catch (error: any) {
    console.error('Error executing command:', error);
    return NextResponse.json(
      { error: `Failed to execute command: ${error.message}` },
      { status: 500 }
    );
  }
} 