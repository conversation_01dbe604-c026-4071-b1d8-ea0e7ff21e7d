import { NextRequest, NextResponse } from 'next/server';
import { dbOrchestration } from '@/lib/microvm';
import { exec } from 'child_process';
import { promisify } from 'util';
import { auth } from '@/auth';

const execAsync = promisify(exec);

export const dynamic = 'force-dynamic';

// Function to get metrics from Docker container
async function getContainerMetrics(containerId: string) {
  try {
    // Get CPU metrics
    const { stdout: cpuData } = await execAsync(
      `docker stats ${containerId} --no-stream --format "{{.CPUPerc}}"`
    );
    
    // Get memory metrics
    const { stdout: memData } = await execAsync(
      `docker stats ${containerId} --no-stream --format "{{.MemUsage}}"`
    );
    
    // Parse CPU percentage (remove % sign)
    const cpuPercentage = parseFloat(cpuData.replace('%', ''));
    
    // Parse memory usage (format: "125MiB / 1.944GiB")
    const memoryUsage = parseFloat(memData.split(' / ')[0].replace('MiB', ''));
    
    return {
      cpu: isNaN(cpuPercentage) ? 0 : cpuPercentage,
      memory: isNaN(memoryUsage) ? 0 : memoryUsage,
    };
  } catch (error) {
    console.error('Error getting container metrics:', error);
    return { cpu: 0, memory: 0 };
  }
}

// GET /api/agents/dashboard - Get metrics for all VMs
export async function GET() {
  // Get the user's session
  const session = await auth();
  if (!session?.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const userId = session.user.id as string;
    const agentsWithVMs = await dbOrchestration.listAgentsWithEnvironments(userId);
    
    // Gather metrics for each VM
    const metricsPromises = agentsWithVMs
      .filter(({ vm }) => vm && vm.status === 'running')
      .map(async ({ agent, vm }) => {
        if (!vm) return null;
        
        const containerId = `microvm-${vm.id}`;
        const metrics = await getContainerMetrics(containerId);
        
        return {
          agentId: agent.id,
          vmId: vm.id,
          name: agent.name,
          role: agent.role,
          status: vm.status,
          metrics: {
            cpu: metrics.cpu,
            memory: metrics.memory,
            timestamp: new Date().toISOString(),
          }
        };
      });
    
    const results = await Promise.all(metricsPromises);
    const filteredResults = results.filter(Boolean);
    
    return NextResponse.json({
      metrics: filteredResults,
      timestamp: new Date().toISOString(),
    });
  } catch (error: any) {
    console.error('Error fetching VM metrics:', error);
    return NextResponse.json(
      { error: `Failed to fetch VM metrics: ${error.message}` },
      { status: 500 }
    );
  }
} 