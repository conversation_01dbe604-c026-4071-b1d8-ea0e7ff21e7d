import { NextRequest, NextResponse } from 'next/server';
import { dbOrchestration } from '@/lib/microvm';
import { auth } from '@/auth';

export const dynamic = 'force-dynamic';

// GET /api/agents - List all agents with their VMs
export async function GET() {
  // Get the user's session
  const session = await auth();
  if (!session?.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
    const userId = session.user.id as string;
    const agentsWithVMs = await dbOrchestration.listAgentsWithEnvironments(userId);
    
    return NextResponse.json({
      agents: agentsWithVMs.map(({ agent, vm }) => ({
        ...agent,
        vm: vm ? {
          id: vm.id,
          status: vm.status,
          name: vm.name,
        } : null,
      })),
    });
  } catch (error: any) {
    console.error('Error fetching agents:', error);
    return NextResponse.json({ error: 'Failed to fetch agents' }, { status: 500 });
  }
}

// POST /api/agents - Create a new agent with VM
export async function POST(req: NextRequest) {
  // Get the user's session
  const session = await auth();
  if (!session?.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
    const body = await req.json();
    
    const { name, role, description, systemPrompt, tools, image } = body;
    
    if (!name || !role || !description || !systemPrompt || !image) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    const { agent, vm } = await dbOrchestration.createAgentWithEnvironment(
      {
        name,
        role,
        description,
        systemPrompt,
        tools,
      },
      {
        image,
        memory: body.memory || 512,
        cpu: body.cpu || 1,
        ports: body.ports || [],
        envVars: body.envVars || {},
      }
    );
    
    return NextResponse.json({
      agent: {
        ...agent,
        vm: {
          id: vm.id,
          status: vm.status,
          name: vm.name,
        },
      },
    });
  } catch (error: any) {
    console.error('Error creating agent:', error);
    return NextResponse.json(
      { error: `Failed to create agent: ${error.message}` },
      { status: 500 }
    );
  }
} 