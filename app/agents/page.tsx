import { <PERSON>ada<PERSON> } from 'next';
import { AgentsList } from '@/components/agents/agents-list';
import { CreateAgentButton } from '@/components/agents/create-agent-button';

export const metadata: Metadata = {
  title: 'AI Agents',
  description: 'Manage your AI agents and their execution environments.',
};

export default function AgentsPage() {
  return (
    <div className="flex flex-col w-full h-full p-4 md:p-8">
      <div className="flex flex-col gap-2">
        <h1 className="text-2xl font-bold">AI Agents</h1>
        <p className="text-muted-foreground">
          Create and manage your AI agents with isolated MicroVM environments.
        </p>
      </div>
      
      <div className="flex justify-end my-4">
        <CreateAgentButton />
      </div>
      
      <div className="flex-1 overflow-hidden">
        <AgentsList />
      </div>
    </div>
  );
} 