# Streamlit Pyodide Implementation

## Overview

This document describes the complete migration from server-side Streamlit subprocess management to client-side execution using Pyodide and stlite. The new implementation enables Streamlit applications to run entirely in the browser without requiring a Python backend server.

## Architecture Changes

### Before: Server-Side Execution
- **Process Manager**: `lib/streamlit/process-manager.ts` managed subprocess execution
- **API Routes**: `/api/streamlit` handled start/stop/update operations
- **File System**: Temporary files created on server filesystem
- **Networking**: Required server-side Python environment and port management

### After: Client-Side Execution
- **Execution Manager**: `lib/streamlit/client-execution-manager.ts` manages browser execution
- **stlite Integration**: Uses `@stlite/browser` for Pyodide-based Streamlit
- **Browser Runtime**: All execution happens in WebAssembly/Pyodide
- **No Server Dependencies**: Completely serverless operation

## Key Components

### 1. Client Execution Manager (`lib/streamlit/client-execution-manager.ts`)

**Purpose**: Manages Streamlit application lifecycle in the browser

**Key Features**:
- Mounts/unmounts stlite applications in DOM elements
- Handles package installation via micropip
- Manages execution state and logging
- Provides real-time output streaming
- Automatic cleanup on page unload

**API**:
```typescript
interface StreamlitClientExecutionManager {
  startStreamlitApp(projectId: string, project: StreamlitProject, mountElement: HTMLElement, onOutput?: (output: ExecutionOutput) => void): Promise<StreamlitExecution>
  stopExecution(projectId: string): Promise<void>
  updateProject(projectId: string, project: StreamlitProject, onOutput?: (output: ExecutionOutput) => void): Promise<StreamlitExecution>
  getExecution(projectId: string): StreamlitExecution | undefined
  getAllExecutions(): StreamlitExecution[]
  cleanup(): Promise<void>
}
```

### 2. Updated StreamlitEditor (`components/streamlit-editor.tsx`)

**Changes**:
- Replaced iframe preview with direct DOM mounting
- Updated to use `StreamlitExecution` instead of `StreamlitProcess`
- Added `previewRef` for DOM element targeting
- Removed server-side API calls

**Preview Integration**:
```tsx
<div 
  ref={previewRef}
  className="w-full h-full border-0 bg-white"
  id={`streamlit-preview-${metadata.execution.projectId}`}
/>
```

### 3. Updated Artifact Actions (`artifacts/streamlit/client.tsx`)

**Run Action**:
- Finds preview DOM element by ID
- Calls `streamlitClientExecutionManager.startStreamlitApp()`
- Handles real-time logging via callback

**Stop Action**:
- Calls `streamlitClientExecutionManager.stopExecution()`
- Updates metadata to reflect stopped state

**Restart Action**:
- Calls `streamlitClientExecutionManager.updateProject()`
- Preserves existing mount element

**Copy Action**:
- Changed from "Copy URL" to "Copy Code"
- Copies main application code to clipboard

## Dependencies

### Added
- `@stlite/browser@^0.83.0`: Production-ready Streamlit browser port

### Removed
- Server-side subprocess management
- API routes for process management
- Temporary file system operations

## Browser Compatibility

### Supported Browsers
- **Chrome**: 57+ (WebAssembly support)
- **Firefox**: 52+ (WebAssembly support)
- **Safari**: 11+ (WebAssembly support)
- **Edge**: 16+ (WebAssembly support)

### Requirements
- WebAssembly support
- Modern JavaScript (ES2018+)
- IndexedDB for file persistence (optional)

## Package Management

### Automatic Requirements Extraction
The execution manager automatically extracts Python packages from `requirements.txt`:

```python
# requirements.txt
streamlit
pandas>=1.0.0
numpy
matplotlib
plotly>=5.0.0
```

### Pyodide Package Installation
Packages are installed via micropip in the browser:
- Pure Python packages: Full compatibility
- Binary extensions: Limited to Pyodide-compatible wheels
- Popular packages: pandas, numpy, matplotlib, plotly, scipy

### Unsupported Packages
- Packages requiring system libraries
- Packages with C extensions not built for Pyodide
- Network-dependent packages (limited HTTP support)

## Performance Considerations

### Advantages
- **No Server Load**: All execution happens client-side
- **Instant Startup**: No subprocess creation overhead
- **Scalability**: Unlimited concurrent users
- **Offline Capable**: Works without internet after initial load

### Limitations
- **Initial Load Time**: Pyodide and packages must download (~50-100MB)
- **Memory Usage**: Limited by browser memory constraints
- **CPU Performance**: WebAssembly is ~2-3x slower than native Python
- **Package Limitations**: Not all Python packages available

## Security Benefits

### Client-Side Execution
- **Sandboxed Environment**: Code runs in browser security sandbox
- **No Server Access**: Cannot access server filesystem or network
- **User Isolation**: Each user's code runs independently
- **No Persistent State**: Execution state cleared on page reload

### Removed Attack Vectors
- No subprocess injection attacks
- No server filesystem access
- No network port exposure
- No server-side code execution

## Migration Guide

### For Developers

1. **Update Imports**:
   ```typescript
   // Old
   import { streamlitProcessManager } from '@/lib/streamlit/process-manager';
   
   // New
   import { streamlitClientExecutionManager } from '@/lib/streamlit/client-execution-manager';
   ```

2. **Update API Calls**:
   ```typescript
   // Old
   const response = await fetch('/api/streamlit?action=start', { ... });
   
   // New
   const execution = await streamlitClientExecutionManager.startStreamlitApp(...);
   ```

3. **Update Preview Handling**:
   ```typescript
   // Old
   <iframe src={process.url} />
   
   // New
   <div ref={previewRef} id={`streamlit-preview-${projectId}`} />
   ```

### For Users

**No Changes Required**: The user experience remains identical:
- Same artifact creation flow
- Same editor interface
- Same run/stop/restart controls
- Same real-time preview

## Testing

### Manual Testing
1. Create a new Streamlit artifact
2. Verify code editor functionality
3. Test run/stop/restart actions
4. Verify real-time preview
5. Check logging output
6. Test package installation

### Example Test Cases
```python
# Basic functionality
import streamlit as st
st.write("Hello, Pyodide!")

# Data processing
import pandas as pd
import numpy as np
df = pd.DataFrame({'x': np.random.randn(100)})
st.dataframe(df)

# Visualization
import matplotlib.pyplot as plt
fig, ax = plt.subplots()
ax.plot([1, 2, 3, 4])
st.pyplot(fig)
```

## Future Enhancements

### Planned Features
1. **File Persistence**: IndexedDB integration for persistent storage
2. **Package Caching**: Cache downloaded packages across sessions
3. **Advanced Debugging**: Enhanced error reporting and debugging tools
4. **Performance Optimization**: Lazy loading and code splitting

### Potential Integrations
1. **Jupyter Integration**: Support for notebook-style execution
2. **Data Connectors**: Browser-compatible data source integrations
3. **Export Options**: Export to standalone HTML files
4. **Collaboration**: Real-time collaborative editing

## Conclusion

The migration to Pyodide-based execution represents a significant architectural improvement:

- **Simplified Deployment**: No Python server requirements
- **Enhanced Security**: Sandboxed browser execution
- **Better Scalability**: Client-side resource utilization
- **Modern Architecture**: Leverages WebAssembly and modern web standards

This implementation provides a production-ready, secure, and scalable solution for running Streamlit applications directly in the browser while maintaining full compatibility with the existing artifact system.
