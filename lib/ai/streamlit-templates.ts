/**
 * Streamlit Application Templates
 * 
 * This module contains pre-built templates for common Streamlit application patterns.
 * These templates serve as starting points for AI generation and ensure consistent,
 * high-quality application structure.
 */

export interface StreamlitTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  complexity: 'simple' | 'medium' | 'complex';
  files: Record<string, string>;
  entryPoint: string;
  requirements: string[];
  features: string[];
  useCases: string[];
}

export const streamlitTemplates: Record<string, StreamlitTemplate> = {
  dataVisualizationDashboard: {
    id: 'data-viz-dashboard',
    name: 'Data Visualization Dashboard',
    description: 'Interactive dashboard for exploring and visualizing datasets',
    category: 'dashboard',
    complexity: 'medium',
    files: {
      'app.py': `import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import plotly.express as px
import plotly.graph_objects as go

st.set_page_config(
    page_title="Data Visualization Dashboard",
    page_icon="📊",
    layout="wide"
)

st.title("📊 Data Visualization Dashboard")
st.markdown("Upload your data and create interactive visualizations")

# Sidebar for controls
st.sidebar.header("Dashboard Controls")

# File upload
uploaded_file = st.sidebar.file_uploader("Upload CSV file", type=['csv'])

if uploaded_file is not None:
    try:
        df = pd.read_csv(uploaded_file)
        st.sidebar.success(f"Loaded {len(df)} rows")
        
        # Data preview
        st.subheader("Data Preview")
        st.dataframe(df.head(), use_container_width=True)
        
        # Column selection
        numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()
        categorical_columns = df.select_dtypes(include=['object']).columns.tolist()
        
        if numeric_columns:
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("Distribution Analysis")
                selected_column = st.selectbox("Select column", numeric_columns)
                
                fig, ax = plt.subplots()
                ax.hist(df[selected_column].dropna(), bins=30, alpha=0.7)
                ax.set_title(f"Distribution of {selected_column}")
                st.pyplot(fig)
            
            with col2:
                st.subheader("Correlation Analysis")
                if len(numeric_columns) > 1:
                    corr_matrix = df[numeric_columns].corr()
                    fig = px.imshow(corr_matrix, text_auto=True, aspect="auto")
                    st.plotly_chart(fig, use_container_width=True)
        
        # Interactive scatter plot
        if len(numeric_columns) >= 2:
            st.subheader("Interactive Scatter Plot")
            x_axis = st.selectbox("X-axis", numeric_columns, key="x")
            y_axis = st.selectbox("Y-axis", numeric_columns, index=1, key="y")
            
            color_by = None
            if categorical_columns:
                color_by = st.selectbox("Color by", ["None"] + categorical_columns)
                color_by = None if color_by == "None" else color_by
            
            fig = px.scatter(df, x=x_axis, y=y_axis, color=color_by,
                           title=f"{y_axis} vs {x_axis}")
            st.plotly_chart(fig, use_container_width=True)
        
    except Exception as e:
        st.error(f"Error loading file: {str(e)}")
else:
    # Sample data demo
    st.info("Upload a CSV file to get started, or explore with sample data below")
    
    # Generate sample data
    np.random.seed(42)
    sample_data = pd.DataFrame({
        'Category': np.random.choice(['A', 'B', 'C'], 100),
        'Value1': np.random.randn(100) * 10 + 50,
        'Value2': np.random.randn(100) * 5 + 25,
        'Date': pd.date_range('2024-01-01', periods=100)
    })
    
    st.subheader("Sample Data Preview")
    st.dataframe(sample_data.head(), use_container_width=True)
    
    col1, col2 = st.columns(2)
    with col1:
        fig = px.scatter(sample_data, x='Value1', y='Value2', color='Category',
                        title="Sample Scatter Plot")
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        fig = px.line(sample_data, x='Date', y='Value1', 
                     title="Sample Time Series")
        st.plotly_chart(fig, use_container_width=True)`,
      'requirements.txt': 'streamlit\npandas\nnumpy\nmatplotlib\nplotly',
    },
    entryPoint: 'app.py',
    requirements: ['streamlit', 'pandas', 'numpy', 'matplotlib', 'plotly'],
    features: ['File upload', 'Data preview', 'Interactive charts', 'Statistical analysis'],
    useCases: ['Data exploration', 'Business intelligence', 'Research analysis', 'Report generation'],
  },

  interactiveCalculator: {
    id: 'interactive-calculator',
    name: 'Interactive Calculator',
    description: 'Multi-purpose calculator with various mathematical functions',
    category: 'calculator',
    complexity: 'simple',
    files: {
      'app.py': `import streamlit as st
import numpy as np
import math

st.set_page_config(
    page_title="Interactive Calculator",
    page_icon="🧮",
    layout="centered"
)

st.title("🧮 Interactive Calculator")
st.markdown("Perform various mathematical calculations with ease")

# Calculator type selection
calc_type = st.selectbox(
    "Choose Calculator Type",
    ["Basic Calculator", "Scientific Calculator", "Financial Calculator", "Unit Converter"]
)

if calc_type == "Basic Calculator":
    st.subheader("Basic Calculator")
    
    col1, col2 = st.columns(2)
    with col1:
        num1 = st.number_input("First Number", value=0.0)
    with col2:
        num2 = st.number_input("Second Number", value=0.0)
    
    operation = st.selectbox("Operation", ["+", "-", "×", "÷", "^"])
    
    if st.button("Calculate"):
        try:
            if operation == "+":
                result = num1 + num2
            elif operation == "-":
                result = num1 - num2
            elif operation == "×":
                result = num1 * num2
            elif operation == "÷":
                result = num1 / num2 if num2 != 0 else "Error: Division by zero"
            elif operation == "^":
                result = num1 ** num2
            
            st.success(f"Result: {result}")
        except Exception as e:
            st.error(f"Error: {str(e)}")

elif calc_type == "Scientific Calculator":
    st.subheader("Scientific Calculator")
    
    number = st.number_input("Enter Number", value=0.0)
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("sin"):
            st.write(f"sin({number}) = {math.sin(math.radians(number)):.6f}")
        if st.button("cos"):
            st.write(f"cos({number}) = {math.cos(math.radians(number)):.6f}")
        if st.button("tan"):
            st.write(f"tan({number}) = {math.tan(math.radians(number)):.6f}")
    
    with col2:
        if st.button("log"):
            if number > 0:
                st.write(f"log({number}) = {math.log10(number):.6f}")
            else:
                st.error("Logarithm undefined for non-positive numbers")
        if st.button("ln"):
            if number > 0:
                st.write(f"ln({number}) = {math.log(number):.6f}")
            else:
                st.error("Natural log undefined for non-positive numbers")
        if st.button("√"):
            if number >= 0:
                st.write(f"√{number} = {math.sqrt(number):.6f}")
            else:
                st.error("Square root undefined for negative numbers")
    
    with col3:
        if st.button("x²"):
            st.write(f"{number}² = {number**2}")
        if st.button("x³"):
            st.write(f"{number}³ = {number**3}")
        if st.button("1/x"):
            if number != 0:
                st.write(f"1/{number} = {1/number:.6f}")
            else:
                st.error("Division by zero")

elif calc_type == "Financial Calculator":
    st.subheader("Financial Calculator")
    
    calc_option = st.selectbox(
        "Choose Calculation",
        ["Compound Interest", "Loan Payment", "Investment Return"]
    )
    
    if calc_option == "Compound Interest":
        principal = st.number_input("Principal Amount ($)", value=1000.0, min_value=0.0)
        rate = st.number_input("Annual Interest Rate (%)", value=5.0, min_value=0.0) / 100
        time = st.number_input("Time Period (years)", value=1.0, min_value=0.0)
        compound_freq = st.selectbox("Compounding Frequency", 
                                   ["Annually", "Semi-annually", "Quarterly", "Monthly", "Daily"])
        
        freq_map = {"Annually": 1, "Semi-annually": 2, "Quarterly": 4, "Monthly": 12, "Daily": 365}
        n = freq_map[compound_freq]
        
        if st.button("Calculate Compound Interest"):
            amount = principal * (1 + rate/n)**(n*time)
            interest = amount - principal
            
            st.success(f"Final Amount: ${amount:.2f}")
            st.info(f"Interest Earned: ${interest:.2f}")

st.markdown("---")
st.markdown("💡 **Tip**: Use this calculator for quick mathematical computations and financial planning!")`,
      'requirements.txt': 'streamlit\nnumpy',
    },
    entryPoint: 'app.py',
    requirements: ['streamlit', 'numpy'],
    features: ['Basic arithmetic', 'Scientific functions', 'Financial calculations', 'Unit conversion'],
    useCases: ['Mathematical calculations', 'Financial planning', 'Educational purposes', 'Quick computations'],
  },
};

export function getTemplateByCategory(category: string): StreamlitTemplate[] {
  return Object.values(streamlitTemplates).filter(template => template.category === category);
}

export function getTemplateById(id: string): StreamlitTemplate | undefined {
  return Object.values(streamlitTemplates).find(template => template.id === id);
}

export function getAllTemplates(): StreamlitTemplate[] {
  return Object.values(streamlitTemplates);
}

export function getTemplatesByComplexity(complexity: 'simple' | 'medium' | 'complex'): StreamlitTemplate[] {
  return Object.values(streamlitTemplates).filter(template => template.complexity === complexity);
}
