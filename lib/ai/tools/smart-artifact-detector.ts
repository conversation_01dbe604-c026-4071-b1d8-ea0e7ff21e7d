import { tool, streamObject } from 'ai';
import { z } from 'zod';
import { myProvider } from '@/lib/ai/providers';

const artifactDetectionPrompt = `
You are an expert AI assistant that analyzes user requests to determine the most appropriate artifact type for their needs.

Analyze the user's request and determine which artifact type would best serve their needs:

## Artifact Types:

### 'streamlit' - Interactive Web Applications
Use when the user wants:
- Interactive dashboards or data visualization tools
- Web applications with user input and real-time updates
- Data analysis tools with filtering, selection, or parameter adjustment
- Calculators, simulators, or interactive demos
- Multi-step workflows or guided processes
- Form-based applications or surveys
- Educational tools or tutorials with interaction
- Business tools requiring user interface
- Apps that benefit from widgets (sliders, dropdowns, file uploads)

### 'code' - Simple Scripts and Functions
Use when the user wants:
- Simple algorithms or mathematical functions
- Data processing scripts without user interaction
- One-time calculations or transformations
- Utility functions or helper code
- Basic examples or demonstrations
- Code snippets for learning purposes
- Scripts that run once and produce output
- Simple data analysis without interactivity

### 'text' - Documentation and Written Content
Use when the user wants:
- Documentation, guides, or explanations
- Essays, reports, or articles
- Structured content or lists
- Meeting notes or summaries
- Creative writing or storytelling

### 'sheet' - Tabular Data
Use when the user wants:
- Spreadsheets or CSV data
- Tables with rows and columns
- Data that needs to be organized in tabular format
- Budget templates or data entry forms

### 'image' - Visual Content
Use when the user wants:
- Diagrams, charts, or illustrations
- Visual representations or graphics
- Images or artwork

## Decision Criteria:

**Choose 'streamlit' if:**
- User mentions: "interactive", "dashboard", "app", "tool", "calculator", "visualize", "explore", "filter", "adjust", "configure"
- Request involves user input, parameter adjustment, or real-time updates
- Multiple steps or workflows are involved
- Data visualization with user control is needed
- Educational or demonstration purposes with interaction

**Choose 'code' if:**
- User mentions: "function", "algorithm", "script", "calculate", "process", "simple", "example"
- Request is for a one-time calculation or data transformation
- No user interaction or interface is needed
- Simple demonstration or learning example
- Utility function or helper code

Analyze the request carefully and consider the user's intent, the complexity of the task, and whether interactivity would add value.
`;

export const smartArtifactDetector = tool({
  description: 'Intelligently analyze user requests to determine the most appropriate artifact type (streamlit vs code vs others)',
  parameters: z.object({
    userRequest: z.string().describe('The user\'s request to analyze'),
    context: z.string().optional().describe('Additional context about the user\'s needs'),
  }),
  execute: async ({ userRequest, context }) => {
    try {
      const streamResult = await streamObject({
        model: myProvider.languageModel('artifact-model'),
        system: artifactDetectionPrompt,
        prompt: `Analyze this user request and determine the best artifact type:

User Request: "${userRequest}"
${context ? `Additional Context: ${context}` : ''}

Consider:
1. Does the user want something interactive or static?
2. Would the user benefit from real-time updates or parameter adjustment?
3. Is this a one-time calculation or an ongoing tool?
4. Does the request involve data visualization or user interface elements?
5. Would widgets (sliders, dropdowns, file uploads) add value?`,
        schema: z.object({
          recommendedType: z.enum(['streamlit', 'code', 'text', 'sheet', 'image']).describe('The recommended artifact type'),
          confidence: z.number().min(0).max(1).describe('Confidence level in the recommendation (0-1)'),
          reasoning: z.string().describe('Detailed explanation of why this type was chosen'),
          alternativeType: z.enum(['streamlit', 'code', 'text', 'sheet', 'image']).optional().describe('Alternative artifact type if confidence is low'),
          keyFeatures: z.array(z.string()).describe('Key features that influenced the decision'),
          interactivityLevel: z.enum(['none', 'low', 'medium', 'high']).describe('Level of interactivity needed'),
          complexity: z.enum(['simple', 'medium', 'complex']).describe('Estimated complexity level'),
          userBenefits: z.array(z.string()).describe('Benefits the user will get from this artifact type'),
        }),
      });

      const object = await streamResult.object;

      return {
        recommendation: object.recommendedType,
        confidence: object.confidence,
        reasoning: object.reasoning,
        alternative: object.alternativeType,
        features: object.keyFeatures,
        interactivity: object.interactivityLevel,
        complexity: object.complexity,
        benefits: object.userBenefits,
      };
    } catch (error) {
      console.error('Error in smart artifact detector:', error);

      // Fallback to keyword-based detection
      const lowerRequest = userRequest.toLowerCase();

      if (lowerRequest.includes('interactive') ||
          lowerRequest.includes('dashboard') ||
          lowerRequest.includes('app') ||
          lowerRequest.includes('tool') ||
          lowerRequest.includes('calculator') ||
          lowerRequest.includes('visualize') ||
          lowerRequest.includes('explore')) {
        return {
          recommendation: 'streamlit' as const,
          confidence: 0.7,
          reasoning: 'Fallback detection based on keywords suggesting interactivity',
          features: ['interactive elements detected'],
          interactivity: 'medium' as const,
          complexity: 'medium' as const,
          benefits: ['Interactive user interface', 'Real-time updates'],
        };
      }

      return {
        recommendation: 'code' as const,
        confidence: 0.6,
        reasoning: 'Fallback to code artifact due to detection error',
        features: ['basic functionality'],
        interactivity: 'none' as const,
        complexity: 'simple' as const,
        benefits: ['Simple implementation', 'Quick execution'],
      };
    }
  },
});

// Helper function to get artifact type recommendation
export async function getArtifactTypeRecommendation(userRequest: string, context?: string) {
  try {
    const result = await smartArtifactDetector.execute({ userRequest, context });
    return result;
  } catch (error) {
    console.error('Error in artifact type detection:', error);
    // Fallback logic
    const lowerRequest = userRequest.toLowerCase();
    
    // Simple keyword-based fallback
    if (lowerRequest.includes('interactive') || 
        lowerRequest.includes('dashboard') || 
        lowerRequest.includes('app') || 
        lowerRequest.includes('tool') ||
        lowerRequest.includes('calculator') ||
        lowerRequest.includes('visualize') ||
        lowerRequest.includes('explore')) {
      return {
        recommendation: 'streamlit' as const,
        confidence: 0.7,
        reasoning: 'Fallback detection based on keywords suggesting interactivity',
        features: ['interactive elements detected'],
        interactivity: 'medium' as const,
        complexity: 'medium' as const,
        benefits: ['Interactive user interface', 'Real-time updates'],
      };
    }
    
    return {
      recommendation: 'code' as const,
      confidence: 0.6,
      reasoning: 'Fallback to code artifact due to detection error',
      features: ['basic functionality'],
      interactivity: 'none' as const,
      complexity: 'simple' as const,
      benefits: ['Simple implementation', 'Quick execution'],
    };
  }
}
