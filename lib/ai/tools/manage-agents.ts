import { tool } from 'ai';
import { z } from 'zod';
import { orchestration } from '@/lib/microvm';
import { AgentRole } from '@/lib/microvm/types';

export const createAgentWithVM = tool({
  description: 'Create a new AI agent with its own dedicated execution environment in a MicroVM',
  parameters: z.object({
    name: z.string().describe('Name of the agent'),
    role: z.enum(['developer', 'designer', 'tester', 'analyst', 'custom'] as const)
      .describe('The role of the agent'),
    description: z.string().describe('Description of the agent\'s purpose'),
    systemPrompt: z.string().describe('The system prompt that defines the agent\'s behavior'),
    tools: z.array(z.string()).optional().describe('List of tool IDs the agent can use'),
    image: z.string().describe('Docker image to use for the VM (e.g., "ubuntu:latest", "node:18", "python:3.9")'),
    memory: z.number().optional().describe('Memory in MB for the VM'),
    cpu: z.number().optional().describe('Number of CPU cores for the VM'),
    ports: z.array(z.number()).optional().describe('Ports to expose from the VM'),
    envVars: z.record(z.string()).optional().describe('Environment variables to set in the VM')
  }),
  execute: async ({ name, role, description, systemPrompt, tools, image, memory, cpu, ports, envVars }) => {
    try {
      const result = await orchestration.createAgentWithEnvironment(
        { 
          name, 
          role: role as AgentRole,
          description, 
          systemPrompt, 
          tools 
        },
        { 
          image, 
          memory, 
          cpu, 
          ports, 
          envVars 
        }
      );
      
      return {
        success: true,
        agent: {
          id: result.agent.id,
          name: result.agent.name,
          role: result.agent.role,
          description: result.agent.description
        },
        vm: {
          id: result.vm.id,
          name: result.vm.name,
          status: result.vm.status,
          image: result.vm.image
        }
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }
});

export const listAgents = tool({
  description: 'List all available AI agents and their environment status',
  parameters: z.object({}),
  execute: async () => {
    try {
      // Using 'system' as the default user ID for this example
      const results = await orchestration.listAgentsWithEnvironments('system');
      
      return {
        success: true,
        agents: results.map(({ agent, vm }) => ({
          id: agent.id,
          name: agent.name,
          role: agent.role,
          description: agent.description,
          vm: vm ? {
            id: vm.id,
            status: vm.status,
            image: vm.image
          } : null
        }))
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }
});

export const startAgentVM = tool({
  description: 'Start an agent\'s VM if it\'s stopped',
  parameters: z.object({
    agentId: z.string().describe('ID of the agent whose VM to start')
  }),
  execute: async ({ agentId }) => {
    try {
      const vm = await orchestration.startAgentVM(agentId);
      
      return {
        success: true,
        vm: {
          id: vm.id,
          name: vm.name,
          status: vm.status
        }
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }
});

export const stopAgentVM = tool({
  description: 'Stop an agent\'s VM if it\'s running',
  parameters: z.object({
    agentId: z.string().describe('ID of the agent whose VM to stop')
  }),
  execute: async ({ agentId }) => {
    try {
      const vm = await orchestration.stopAgentVM(agentId);
      
      return {
        success: true,
        vm: {
          id: vm.id,
          name: vm.name,
          status: vm.status
        }
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }
});

export const deleteAgent = tool({
  description: 'Delete an agent and its associated VM',
  parameters: z.object({
    agentId: z.string().describe('ID of the agent to delete')
  }),
  execute: async ({ agentId }) => {
    try {
      await orchestration.deleteAgentWithEnvironment(agentId);
      
      return {
        success: true,
        message: `Agent with ID ${agentId} and its VM have been deleted`
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }
}); 