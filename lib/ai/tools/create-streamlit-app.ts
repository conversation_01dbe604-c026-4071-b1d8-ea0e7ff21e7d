import { generateUUID } from '@/lib/utils';
import { DataStreamWriter, tool, streamObject } from 'ai';
import { z } from 'zod';
import { Session } from 'next-auth';
import { myProvider } from '@/lib/ai/providers';
import { saveDocument } from '@/lib/db/queries';

interface CreateStreamlitAppProps {
  session: Session;
  dataStream: DataStreamWriter;
}

const streamlitAppAnalysisPrompt = `
You are an expert Streamlit application architect. Analyze the user's request and determine the best approach for creating a Streamlit application.

Consider these factors:
1. **App Category**: What type of application is this? (dashboard, calculator, analysis tool, demo, etc.)
2. **Complexity Level**: Simple (single page), Medium (multiple sections), Complex (multi-page app)
3. **Data Requirements**: What data sources or types are needed?
4. **Interactivity Level**: Basic widgets, Advanced interactions, Real-time updates
5. **Target Audience**: Technical users, Business users, General public
6. **Key Features**: What are the main functionalities the app should provide?

Based on your analysis, provide:
- A detailed app specification
- Recommended file structure
- Key libraries and dependencies
- User experience flow
- Technical implementation approach
`;

const streamlitAppGenerationPrompt = `
You are an expert Streamlit developer creating a production-ready application based on the provided specification.

Create a complete, functional Streamlit application that:
1. **Follows Best Practices**: Clean code, proper structure, comprehensive comments
2. **Optimized for Browser**: Uses Pyodide-compatible libraries, efficient processing
3. **User-Friendly**: Intuitive interface, helpful guidance, error handling
4. **Professional Quality**: Polished UI, consistent styling, engaging experience
5. **Practical Value**: Solves real problems, provides actionable insights

File Structure Requirements:
- app.py: Main application with complete functionality
- utils.py: Helper functions (if needed for complex apps)
- requirements.txt: All necessary dependencies
- README.md: Comprehensive documentation
- data/: Sample data files (if applicable)

Technical Requirements:
- Use st.set_page_config() for proper configuration
- Implement proper error handling and validation
- Add loading indicators for long operations
- Use st.cache_data for performance optimization
- Include helpful tooltips and explanations
- Provide sample data or examples when appropriate

UI/UX Requirements:
- Clear title and description
- Logical layout with sections
- Responsive design with columns
- Sidebar for controls when appropriate
- User feedback with success/warning/error messages
- Professional styling and formatting
`;

export const createStreamlitApp = ({ session, dataStream }: CreateStreamlitAppProps) =>
  tool({
    description: 'Create an intelligent, context-aware Streamlit application based on user requirements. This tool analyzes the request and generates a complete, production-ready Streamlit app.',
    parameters: z.object({
      userRequest: z.string().describe('The user\'s request for the Streamlit application'),
      complexity: z.enum(['simple', 'medium', 'complex']).optional().describe('Desired complexity level'),
      category: z.enum(['dashboard', 'calculator', 'analysis', 'demo', 'form', 'game', 'educational', 'business']).optional().describe('Application category'),
    }),
    execute: async ({ userRequest, complexity = 'medium', category }) => {
      const id = generateUUID();

      try {
        // Step 1: Analyze the request and create app specification
        dataStream.writeData({
          type: 'kind',
          content: 'streamlit',
        });

        dataStream.writeData({
          type: 'id',
          content: id,
        });

        dataStream.writeData({
          type: 'clear',
          content: '',
        });

      // Analyze user request to determine app specification
      const streamResult = await streamObject({
        model: myProvider.languageModel('artifact-model'),
        system: streamlitAppAnalysisPrompt,
        prompt: `User Request: ${userRequest}\nComplexity: ${complexity}\nCategory: ${category || 'auto-detect'}`,
        schema: z.object({
          title: z.string().describe('Application title'),
          description: z.string().describe('Detailed description of the application'),
          category: z.string().describe('Application category'),
          complexity: z.string().describe('Complexity level'),
          keyFeatures: z.array(z.string()).describe('List of key features'),
          dataRequirements: z.string().describe('Data requirements and sources'),
          targetAudience: z.string().describe('Target audience'),
          technicalApproach: z.string().describe('Technical implementation approach'),
          recommendedLibraries: z.array(z.string()).describe('Recommended Python libraries'),
          userFlow: z.string().describe('User experience flow'),
        }),
      });

      const appSpec = await streamResult.object;

      dataStream.writeData({
        type: 'title',
        content: appSpec.title,
      });

      // Step 2: Generate the complete Streamlit application
      let draftContent = '';

      const { fullStream } = streamObject({
        model: myProvider.languageModel('artifact-model'),
        system: streamlitAppGenerationPrompt,
        prompt: `Create a Streamlit application based on this specification:

Title: ${appSpec.title}
Description: ${appSpec.description}
Category: ${appSpec.category}
Complexity: ${appSpec.complexity}
Key Features: ${appSpec.keyFeatures.join(', ')}
Data Requirements: ${appSpec.dataRequirements}
Target Audience: ${appSpec.targetAudience}
Technical Approach: ${appSpec.technicalApproach}
Recommended Libraries: ${appSpec.recommendedLibraries.join(', ')}
User Flow: ${appSpec.userFlow}

Original User Request: ${userRequest}`,
        schema: z.object({
          files: z.record(z.string()).describe('Object containing file paths as keys and file contents as values'),
          entryPoint: z.string().describe('Main file to run (usually app.py)'),
          title: z.string().describe('Application title'),
          description: z.string().describe('Brief description of the application'),
          features: z.array(z.string()).describe('List of implemented features'),
          usage: z.string().describe('How to use the application'),
        }),
      });

      for await (const delta of fullStream) {
        const { type } = delta;

        if (type === 'object') {
          const { object } = delta;
          
          if (object.files || object.entryPoint || object.title || object.description) {
            const streamlitProject = {
              files: object.files || {},
              entryPoint: object.entryPoint || 'app.py',
              title: object.title || appSpec.title,
              description: object.description || appSpec.description,
              features: object.features || appSpec.keyFeatures,
              usage: object.usage || 'Follow the instructions in the app interface',
            };

            draftContent = JSON.stringify(streamlitProject, null, 2);

            dataStream.writeData({
              type: 'streamlit-delta',
              content: draftContent,
            });
          }
        }
      }

      // Save the document
      if (session?.user?.id) {
        await saveDocument({
          id,
          title: appSpec.title,
          content: draftContent,
          kind: 'streamlit',
          userId: session.user.id,
        });
      }

      dataStream.writeData({ type: 'finish', content: '' });

        return {
          id,
          title: appSpec.title,
          kind: 'streamlit' as const,
          content: 'An intelligent Streamlit application has been created and is now visible to the user.',
          specification: appSpec,
        };
      } catch (error) {
        console.error('Error creating Streamlit app:', error);

        // Send error to data stream
        dataStream.writeData({
          type: 'streamlit-delta',
          content: JSON.stringify({
            files: {
              'app.py': `import streamlit as st

st.title("⚠️ Error Creating Application")
st.error("There was an error generating the Streamlit application. Please try again with a different request.")
st.markdown("**Error details:** Application generation failed during processing.")

st.markdown("---")
st.markdown("💡 **Tip**: Try being more specific about what you want the application to do.")`,
              'requirements.txt': 'streamlit'
            },
            entryPoint: 'app.py',
            title: 'Error - Application Generation Failed',
            description: 'An error occurred during application generation'
          }, null, 2)
        });

        dataStream.writeData({ type: 'finish', content: '' });

        return {
          id,
          title: 'Error - Application Generation Failed',
          kind: 'streamlit' as const,
          content: 'An error occurred while creating the Streamlit application.',
          error: error instanceof Error ? error.message : 'Unknown error',
        };
      }
    },
  });
