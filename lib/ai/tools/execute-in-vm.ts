import { tool } from 'ai';
import { z } from 'zod';
import { orchestration } from '@/lib/microvm';

export const executeInVM = tool({
  description: 'Execute a command in a specific agent VM environment',
  parameters: z.object({
    agentId: z.string().describe('The ID of the agent whose VM to execute in'),
    command: z.string().describe('The command to execute in the VM')
  }),
  execute: async ({ agentId, command }) => {
    try {
      const result = await orchestration.executeAgentCommand(agentId, command);
      
      if (result.exitCode !== 0) {
        return {
          success: false,
          exitCode: result.exitCode,
          stderr: result.stderr,
          stdout: result.stdout,
          error: `Command failed with exit code ${result.exitCode}`
        };
      }
      
      return {
        success: true,
        exitCode: result.exitCode,
        stdout: result.stdout,
        stderr: result.stderr
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        exitCode: -1,
        stdout: '',
        stderr: error.message
      };
    }
  }
}); 