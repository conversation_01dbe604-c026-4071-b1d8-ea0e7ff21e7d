import { ModuleResolution, PackageInfo, CDNProvider } from '../types';

/**
 * Package manager for React sandbox
 * Handles dynamic package resolution and loading from CDNs
 */
export class SandboxPackageManager {
  private cache = new Map<string, any>();
  private cdnProviders: CDNProvider[] = [];
  private loadedPackages = new Map<string, ModuleResolution>();

  constructor() {
    this.initializeCDNProviders();
  }

  /**
   * Initialize CDN providers
   */
  private initializeCDNProviders() {
    // ESM.sh provider
    this.cdnProviders.push({
      name: 'esm.sh',
      baseUrl: 'https://esm.sh',
      resolvePackage: async (name: string, version = 'latest') => {
        const url = `https://esm.sh/${name}@${version}`;
        const response = await fetch(url);
        
        if (!response.ok) {
          throw new Error(`Package ${name}@${version} not found`);
        }

        return {
          name,
          version,
          url,
          dependencies: {},
          exports: { default: url }
        };
      },
      getPackageInfo: async (name: string) => {
        const response = await fetch(`https://registry.npmjs.org/${name}`);
        if (!response.ok) {
          throw new Error(`Package info for ${name} not found`);
        }
        return response.json();
      }
    });

    // Skypack provider
    this.cdnProviders.push({
      name: 'skypack',
      baseUrl: 'https://cdn.skypack.dev',
      resolvePackage: async (name: string, version = 'latest') => {
        const url = `https://cdn.skypack.dev/${name}@${version}`;
        return {
          name,
          version,
          url,
          dependencies: {},
          exports: { default: url }
        };
      },
      getPackageInfo: async (name: string) => {
        const response = await fetch(`https://registry.npmjs.org/${name}`);
        if (!response.ok) {
          throw new Error(`Package info for ${name} not found`);
        }
        return response.json();
      }
    });

    // UNPKG provider
    this.cdnProviders.push({
      name: 'unpkg',
      baseUrl: 'https://unpkg.com',
      resolvePackage: async (name: string, version = 'latest') => {
        const url = `https://unpkg.com/${name}@${version}/dist/index.js`;
        return {
          name,
          version,
          url,
          dependencies: {},
          exports: { default: url }
        };
      },
      getPackageInfo: async (name: string) => {
        const response = await fetch(`https://registry.npmjs.org/${name}`);
        if (!response.ok) {
          throw new Error(`Package info for ${name} not found`);
        }
        return response.json();
      }
    });
  }

  /**
   * Install a package dynamically
   */
  async installPackage(name: string, version = 'latest'): Promise<any> {
    const cacheKey = `${name}@${version}`;
    
    // Check cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    // Try each CDN provider
    for (const provider of this.cdnProviders) {
      try {
        const resolution = await provider.resolvePackage(name, version);
        const module = await this.loadModule(resolution);
        
        this.cache.set(cacheKey, module);
        this.loadedPackages.set(cacheKey, resolution);
        
        return module;
      } catch (error) {
        console.warn(`Failed to load ${name} from ${provider.name}:`, error);
        continue;
      }
    }

    throw new Error(`Failed to install package ${name}@${version} from all providers`);
  }

  /**
   * Load module from URL
   */
  private async loadModule(resolution: ModuleResolution): Promise<any> {
    try {
      // For ES modules
      if (resolution.url.includes('esm.sh') || resolution.url.includes('skypack')) {
        return await import(resolution.url);
      }

      // For UMD modules
      return await this.loadUMDModule(resolution.url);
    } catch (error) {
      throw new Error(`Failed to load module from ${resolution.url}: ${error}`);
    }
  }

  /**
   * Load UMD module using script tag
   */
  private async loadUMDModule(url: string): Promise<any> {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = url;
      script.onload = () => {
        // Try to find the module in common global locations
        const globalNames = [
          url.split('/').pop()?.split('.')[0],
          'exports',
          'module'
        ];

        for (const name of globalNames) {
          if (name && (window as any)[name]) {
            resolve((window as any)[name]);
            return;
          }
        }

        reject(new Error('Module not found in global scope'));
      };
      script.onerror = () => reject(new Error(`Failed to load script: ${url}`));
      document.head.appendChild(script);
    });
  }

  /**
   * Get package information
   */
  async getPackageInfo(name: string): Promise<PackageInfo> {
    for (const provider of this.cdnProviders) {
      try {
        return await provider.getPackageInfo(name);
      } catch (error) {
        continue;
      }
    }

    throw new Error(`Package info for ${name} not found`);
  }

  /**
   * Install multiple packages
   */
  async installPackages(dependencies: Record<string, string>): Promise<Record<string, any>> {
    const results: Record<string, any> = {};
    const promises = Object.entries(dependencies).map(async ([name, version]) => {
      try {
        const module = await this.installPackage(name, version);
        results[name] = module;
      } catch (error) {
        console.error(`Failed to install ${name}@${version}:`, error);
        throw error;
      }
    });

    await Promise.all(promises);
    return results;
  }

  /**
   * Check if package is installed
   */
  isPackageInstalled(name: string, version = 'latest'): boolean {
    const cacheKey = `${name}@${version}`;
    return this.cache.has(cacheKey);
  }

  /**
   * Get installed packages
   */
  getInstalledPackages(): string[] {
    return Array.from(this.loadedPackages.keys());
  }

  /**
   * Clear package cache
   */
  clearCache(): void {
    this.cache.clear();
    this.loadedPackages.clear();
  }

  /**
   * Create require function for sandbox
   */
  createRequireFunction(): (name: string) => any {
    return (name: string) => {
      // Check for built-in modules
      if (name === 'react') {
        return this.getReactModule();
      }
      
      if (name === 'react-dom') {
        return this.getReactDOMModule();
      }

      // Check cache
      for (const [key, module] of this.cache.entries()) {
        if (key.startsWith(name + '@')) {
          return module;
        }
      }

      throw new Error(`Module '${name}' not found. Install it first using installPackage()`);
    };
  }

  /**
   * Get React module (built-in)
   */
  private getReactModule(): any {
    // Return the React instance from the host environment
    return (window as any).React || require('react');
  }

  /**
   * Get ReactDOM module (built-in)
   */
  private getReactDOMModule(): any {
    // Return the ReactDOM instance from the host environment
    return (window as any).ReactDOM || require('react-dom');
  }

  /**
   * Preload common packages
   */
  async preloadCommonPackages(): Promise<void> {
    const commonPackages = [
      'lodash',
      'axios',
      'moment',
      'uuid',
      'classnames'
    ];

    const promises = commonPackages.map(pkg => 
      this.installPackage(pkg).catch(err => 
        console.warn(`Failed to preload ${pkg}:`, err)
      )
    );

    await Promise.allSettled(promises);
  }

  /**
   * Search packages
   */
  async searchPackages(query: string, limit = 10): Promise<PackageInfo[]> {
    try {
      const response = await fetch(
        `https://registry.npmjs.org/-/v1/search?text=${encodeURIComponent(query)}&size=${limit}`
      );
      
      if (!response.ok) {
        throw new Error('Search failed');
      }

      const data = await response.json();
      return data.objects.map((obj: any) => obj.package);
    } catch (error) {
      console.error('Package search failed:', error);
      return [];
    }
  }
}
