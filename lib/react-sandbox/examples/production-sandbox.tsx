'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  Activity, 
  Zap, 
  Shield, 
  Monitor, 
  AlertTriangle,
  CheckCircle,
  Clock,
  MemoryStick
} from 'lucide-react';

import { ReactSandbox } from '../components/react-sandbox';
import { SandboxErrorBoundary } from '../components/error-boundary';
import { useReactSandbox } from '../hooks/use-react-sandbox';
import { performanceMonitor } from '../utils/performance-monitor';
import { getSandboxConfig } from '../config/production';

/**
 * Production-ready React Sandbox with monitoring, error handling, and security
 */
export function ProductionReactSandbox() {
  const {
    project,
    createProject,
    updateProject,
    availableTemplates,
    isRunning,
    logs,
    errors
  } = useReactSandbox();

  const [performanceData, setPerformanceData] = useState<any>(null);
  const [securityStatus, setSecurityStatus] = useState<'secure' | 'warning' | 'error'>('secure');
  const [systemHealth, setSystemHealth] = useState({
    memory: 0,
    cpu: 0,
    network: 'good'
  });

  // Monitor performance metrics
  useEffect(() => {
    if (!project) return;

    const interval = setInterval(() => {
      const metrics = performanceMonitor.getMetrics(project.id);
      if (metrics) {
        setPerformanceData(performanceMonitor.getPerformanceSummary(project.id));
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [project]);

  // Monitor system health
  useEffect(() => {
    const checkSystemHealth = () => {
      // Check memory usage
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const memoryUsage = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
        
        setSystemHealth(prev => ({
          ...prev,
          memory: memoryUsage
        }));

        // Update security status based on memory usage
        if (memoryUsage > 90) {
          setSecurityStatus('error');
        } else if (memoryUsage > 70) {
          setSecurityStatus('warning');
        } else {
          setSecurityStatus('secure');
        }
      }
    };

    const interval = setInterval(checkSystemHealth, 2000);
    return () => clearInterval(interval);
  }, []);

  const handleCreateProject = (templateId: string) => {
    const template = availableTemplates.find(t => t.id === templateId);
    if (template) {
      createProject(template, `Production ${template.name}`);
    }
  };

  const getSecurityStatusColor = () => {
    switch (securityStatus) {
      case 'secure': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getSecurityStatusIcon = () => {
    switch (securityStatus) {
      case 'secure': return <CheckCircle className="h-4 w-4" />;
      case 'warning': return <AlertTriangle className="h-4 w-4" />;
      case 'error': return <AlertTriangle className="h-4 w-4" />;
      default: return <Shield className="h-4 w-4" />;
    }
  };

  if (!project) {
    return (
      <SandboxErrorBoundary>
        <div className="container mx-auto p-6">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold mb-4">Production React Sandbox</h1>
            <p className="text-muted-foreground">
              Enterprise-grade React development environment with monitoring and security
            </p>
          </div>

          {/* System Status */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Monitor className="h-5 w-5" />
                System Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-full ${getSecurityStatusColor()}`}>
                    {getSecurityStatusIcon()}
                  </div>
                  <div>
                    <p className="text-sm font-medium">Security</p>
                    <p className="text-xs text-muted-foreground capitalize">{securityStatus}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-full text-blue-600">
                    <MemoryStick className="h-4 w-4" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Memory</p>
                    <p className="text-xs text-muted-foreground">{systemHealth.memory.toFixed(1)}%</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-full text-green-600">
                    <Activity className="h-4 w-4" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Network</p>
                    <p className="text-xs text-muted-foreground capitalize">{systemHealth.network}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Templates */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {availableTemplates.map(template => (
              <Card 
                key={template.id} 
                className="cursor-pointer hover:shadow-lg transition-shadow"
                onClick={() => handleCreateProject(template.id)}
              >
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    {template.name}
                    <div className="flex gap-1">
                      <Badge variant="outline">
                        {template.settings.typescript ? 'TS' : 'JS'}
                      </Badge>
                      <Badge variant="secondary">
                        <Shield className="h-3 w-3 mr-1" />
                        Secure
                      </Badge>
                    </div>
                  </CardTitle>
                </CardHeader>
                
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    {template.description}
                  </p>
                  
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-xs">
                      <Zap className="h-3 w-3 text-yellow-500" />
                      <span>Hot reload enabled</span>
                    </div>
                    <div className="flex items-center gap-2 text-xs">
                      <Shield className="h-3 w-3 text-green-500" />
                      <span>Sandboxed execution</span>
                    </div>
                    <div className="flex items-center gap-2 text-xs">
                      <Monitor className="h-3 w-3 text-blue-500" />
                      <span>Performance monitoring</span>
                    </div>
                  </div>
                  
                  <Button className="w-full mt-4">
                    Create Secure Project
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </SandboxErrorBoundary>
    );
  }

  return (
    <SandboxErrorBoundary>
      <div className="h-screen flex flex-col">
        {/* Header with monitoring */}
        <div className="flex items-center justify-between p-4 border-b bg-muted/30">
          <div className="flex items-center gap-3">
            <h1 className="text-xl font-bold">{project.name}</h1>
            <Badge variant={isRunning ? "default" : "secondary"}>
              {isRunning ? 'Running' : 'Stopped'}
            </Badge>
            <Badge variant="outline" className={getSecurityStatusColor()}>
              {getSecurityStatusIcon()}
              {securityStatus}
            </Badge>
          </div>
          
          <div className="flex items-center gap-4">
            {/* Performance indicators */}
            {performanceData && (
              <div className="flex items-center gap-3 text-sm">
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  <span>{performanceData.buildTime}ms</span>
                </div>
                <div className="flex items-center gap-1">
                  <MemoryStick className="h-3 w-3" />
                  <span>{performanceData.memoryUsage}</span>
                </div>
                <Badge variant={performanceData.score > 80 ? "default" : "destructive"}>
                  Score: {performanceData.score}
                </Badge>
              </div>
            )}
            
            <Button
              variant="outline"
              onClick={() => window.location.reload()}
            >
              Reset Environment
            </Button>
          </div>
        </div>

        {/* Alerts */}
        {errors.length > 0 && (
          <Alert variant="destructive" className="m-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {errors.length} error(s) detected. Check console for details.
            </AlertDescription>
          </Alert>
        )}

        {systemHealth.memory > 80 && (
          <Alert variant="destructive" className="m-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              High memory usage detected ({systemHealth.memory.toFixed(1)}%). 
              Consider refreshing the environment.
            </AlertDescription>
          </Alert>
        )}

        {/* Main content */}
        <div className="flex-1">
          <Tabs defaultValue="sandbox" className="h-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="sandbox">Sandbox</TabsTrigger>
              <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
              <TabsTrigger value="security">Security</TabsTrigger>
            </TabsList>
            
            <TabsContent value="sandbox" className="h-full mt-0">
              <ReactSandbox
                project={project}
                onProjectChange={updateProject}
              />
            </TabsContent>
            
            <TabsContent value="monitoring" className="p-6">
              <div className="space-y-6">
                <h2 className="text-2xl font-bold">Performance Monitoring</h2>
                
                {performanceData && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">Build Time</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">{performanceData.buildTime}ms</div>
                        <Progress value={Math.min(100, (performanceData.buildTime / 5000) * 100)} className="mt-2" />
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">Bundle Size</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">{performanceData.bundleSize}</div>
                        <Progress value={50} className="mt-2" />
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">Memory Usage</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">{performanceData.memoryUsage}</div>
                        <Progress value={systemHealth.memory} className="mt-2" />
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">Performance Score</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">{performanceData.score}/100</div>
                        <Progress value={performanceData.score} className="mt-2" />
                      </CardContent>
                    </Card>
                  </div>
                )}
              </div>
            </TabsContent>
            
            <TabsContent value="security" className="p-6">
              <div className="space-y-6">
                <h2 className="text-2xl font-bold">Security Status</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Sandbox Security</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span>Code Isolation</span>
                        <Badge variant="default">Active</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>Memory Limits</span>
                        <Badge variant="default">Enforced</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>Network Restrictions</span>
                        <Badge variant="default">Active</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>API Restrictions</span>
                        <Badge variant="default">Active</Badge>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader>
                      <CardTitle>Resource Monitoring</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span>Memory Usage</span>
                        <span>{systemHealth.memory.toFixed(1)}%</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>Active Logs</span>
                        <span>{logs.length}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>Error Count</span>
                        <span>{errors.length}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>Network Status</span>
                        <Badge variant="default">{systemHealth.network}</Badge>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </SandboxErrorBoundary>
  );
}

export default ProductionReactSandbox;
