'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ReactSandbox } from '../components/react-sandbox';
import { useReactSandbox } from '../hooks/use-react-sandbox';
import { availableTemplates } from '../utils/templates';

/**
 * Basic React Sandbox Example
 * 
 * This component demonstrates how to use the React Sandbox library
 * to create a complete React development environment in the browser.
 */
export function BasicReactSandboxExample() {
  const {
    project,
    createProject,
    updateProject,
    availableTemplates: templates
  } = useReactSandbox();

  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);

  const handleCreateProject = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (template) {
      createProject(template, `My ${template.name}`);
      setSelectedTemplate(templateId);
    }
  };

  if (!project) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-4">React Sandbox</h1>
          <p className="text-muted-foreground">
            Choose a template to start building your React application
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {templates.map(template => (
            <Card 
              key={template.id} 
              className="cursor-pointer hover:shadow-lg transition-shadow"
              onClick={() => handleCreateProject(template.id)}
            >
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  {template.name}
                  <Badge variant="outline">
                    {template.settings.typescript ? 'TS' : 'JS'}
                  </Badge>
                </CardTitle>
                <CardDescription>
                  {template.description}
                </CardDescription>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-2">
                  <div className="text-sm">
                    <strong>Dependencies:</strong>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {Object.keys(template.dependencies).map(dep => (
                        <Badge key={dep} variant="secondary" className="text-xs">
                          {dep}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <div className="text-sm">
                    <strong>Features:</strong>
                    <ul className="list-disc list-inside text-xs text-muted-foreground mt-1">
                      {template.settings.typescript && <li>TypeScript support</li>}
                      {template.settings.jsx && <li>JSX syntax</li>}
                      {template.settings.hotReload && <li>Hot reload</li>}
                      <li>Live preview</li>
                      <li>Console debugging</li>
                    </ul>
                  </div>
                </div>
                
                <Button className="w-full mt-4">
                  Create Project
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-12 text-center">
          <h2 className="text-xl font-semibold mb-4">Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">🔥 Hot Reload</h3>
              <p className="text-sm text-muted-foreground">
                See changes instantly as you type
              </p>
            </div>
            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">📦 Package Manager</h3>
              <p className="text-sm text-muted-foreground">
                Install npm packages on the fly
              </p>
            </div>
            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">🐛 Console</h3>
              <p className="text-sm text-muted-foreground">
                Debug with integrated console
              </p>
            </div>
            <div className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">⚡ Fast Build</h3>
              <p className="text-sm text-muted-foreground">
                Instant compilation and preview
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-3">
          <h1 className="text-xl font-bold">{project.name}</h1>
          <Badge variant="outline">
            {project.settings.typescript ? 'TypeScript' : 'JavaScript'}
          </Badge>
          <Badge variant="secondary">
            {Object.keys(project.files).length} files
          </Badge>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => {
              setSelectedTemplate(null);
              // Reset to template selection
              window.location.reload();
            }}
          >
            New Project
          </Button>
        </div>
      </div>

      {/* Sandbox */}
      <div className="flex-1">
        <ReactSandbox
          project={project}
          onProjectChange={updateProject}
        />
      </div>
    </div>
  );
}

export default BasicReactSandboxExample;
