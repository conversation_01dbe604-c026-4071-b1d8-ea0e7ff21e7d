# React Sandbox Library

A powerful, browser-based React development environment that allows you to create, edit, and run React applications entirely in the browser. Perfect for prototyping, learning, and building React apps without any local setup.

## Features

- 🔥 **Hot Reload** - See changes instantly as you type
- 📦 **Package Manager** - Install npm packages dynamically from CDNs
- 🐛 **Integrated Console** - Debug with full console support
- ⚡ **Fast Compilation** - Instant Babel transpilation
- 🎨 **Syntax Highlighting** - Full CodeMirror editor integration
- 📁 **File Management** - Complete file explorer with CRUD operations
- 🔧 **TypeScript Support** - Full TypeScript compilation
- 🎯 **Error Handling** - Comprehensive error reporting and debugging
- 📱 **Responsive Design** - Works on desktop and mobile
- 🚀 **Templates** - Pre-built project templates to get started quickly

## Quick Start

```tsx
import { ReactSandbox, useReactSandbox } from '@/lib/react-sandbox';

function MyApp() {
  const { project, createProject, availableTemplates } = useReactSandbox();

  // Create a project from template
  const handleCreateProject = () => {
    const template = availableTemplates[0]; // Basic React template
    createProject(template, 'My React App');
  };

  return (
    <div className="h-screen">
      {project ? (
        <ReactSandbox project={project} />
      ) : (
        <button onClick={handleCreateProject}>
          Create React App
        </button>
      )}
    </div>
  );
}
```

## Architecture

### Core Components

- **ReactSandboxRuntime** - Manages code execution and React rendering
- **ReactTranspiler** - Handles Babel transpilation for JSX/TypeScript
- **SandboxPackageManager** - Dynamic package installation from CDNs
- **ReactPreview** - Live preview component with error handling
- **SandboxConsole** - Integrated console for debugging
- **DependencyManager** - UI for managing npm packages

### Key Features

#### 1. Code Transpilation
```typescript
import { ReactTranspiler } from '@/lib/react-sandbox';

const transpiler = new ReactTranspiler();
const result = await transpiler.transpileFile('App.jsx', code, {
  jsx: true,
  typescript: false,
  sourceMaps: true
});
```

#### 2. Package Management
```typescript
import { SandboxPackageManager } from '@/lib/react-sandbox';

const packageManager = new SandboxPackageManager();
await packageManager.installPackage('lodash', 'latest');
```

#### 3. Runtime Execution
```typescript
import { ReactSandboxRuntime } from '@/lib/react-sandbox';

const runtime = new ReactSandboxRuntime();
const execution = await runtime.runProject(project, mountElement);
```

## Usage Examples

### Basic Setup

```tsx
import { ReactSandbox, createProjectFromTemplate, basicReactTemplate } from '@/lib/react-sandbox';

function BasicExample() {
  const project = createProjectFromTemplate(basicReactTemplate, 'My App');

  return (
    <ReactSandbox 
      project={project}
      onProjectChange={(updatedProject) => {
        console.log('Project updated:', updatedProject);
      }}
    />
  );
}
```

### Custom Project

```tsx
import { ReactSandbox } from '@/lib/react-sandbox';

const customProject = {
  id: 'custom-1',
  name: 'Custom React App',
  files: {
    'App.jsx': `
      import React, { useState } from 'react';
      
      function App() {
        const [count, setCount] = useState(0);
        
        return (
          <div>
            <h1>Count: {count}</h1>
            <button onClick={() => setCount(count + 1)}>
              Increment
            </button>
          </div>
        );
      }
      
      export default App;
    `,
    'index.js': `
      import React from 'react';
      import ReactDOM from 'react-dom/client';
      import App from './App';
      
      const root = ReactDOM.createRoot(document.getElementById('root'));
      root.render(<App />);
    `
  },
  dependencies: {
    'react': '^18.0.0',
    'react-dom': '^18.0.0'
  },
  entryPoint: 'index.js',
  settings: {
    typescript: false,
    jsx: true,
    hotReload: true,
    autoSave: true,
    showConsole: true,
    showInspector: false,
    bundler: 'webpack',
    target: 'es2020'
  }
};

function CustomExample() {
  return <ReactSandbox project={customProject} />;
}
```

### Using the Hook

```tsx
import { useReactSandbox } from '@/lib/react-sandbox';

function HookExample() {
  const {
    project,
    createProject,
    updateFile,
    installDependency,
    runProject,
    logs,
    errors,
    isRunning
  } = useReactSandbox();

  const handleCreateProject = () => {
    createProject(basicReactTemplate, 'Hook Example');
  };

  const handleUpdateCode = () => {
    updateFile('App.jsx', `
      import React from 'react';
      
      function App() {
        return <h1>Updated with hook!</h1>;
      }
      
      export default App;
    `);
  };

  return (
    <div>
      {!project && (
        <button onClick={handleCreateProject}>
          Create Project
        </button>
      )}
      
      {project && (
        <div>
          <button onClick={handleUpdateCode}>
            Update Code
          </button>
          <button onClick={() => installDependency('axios')}>
            Install Axios
          </button>
          <p>Status: {isRunning ? 'Running' : 'Stopped'}</p>
          <p>Logs: {logs.length}</p>
          <p>Errors: {errors.length}</p>
        </div>
      )}
    </div>
  );
}
```

## Available Templates

### Basic React Template
- Simple React app with counter
- JSX support
- CSS styling
- Event handling example

### TypeScript React Template
- TypeScript support
- Type-safe components
- Interface definitions
- Advanced React patterns

### React Hooks Example
- Comprehensive hooks demonstration
- useState, useEffect, useReducer, useContext
- Custom hooks
- State management patterns

## Configuration

### Project Settings
```typescript
interface ProjectSettings {
  typescript: boolean;      // Enable TypeScript compilation
  jsx: boolean;            // Enable JSX syntax
  hotReload: boolean;      // Enable hot reload
  autoSave: boolean;       // Auto-save files
  showConsole: boolean;    // Show console panel
  showInspector: boolean;  // Show React DevTools
  bundler: 'webpack' | 'vite' | 'esbuild';
  target: 'es2015' | 'es2017' | 'es2018' | 'es2020' | 'esnext';
}
```

### Sandbox Configuration
```typescript
interface SandboxConfig {
  allowedDomains: string[];    // Allowed domains for network requests
  maxMemory: number;           // Maximum memory usage (bytes)
  timeout: number;             // Execution timeout (ms)
  enableNetworking: boolean;   // Allow network requests
  enableFileSystem: boolean;   // Enable file system access
  enableConsole: boolean;      // Enable console output
  enableDebugger: boolean;     // Enable debugger
  restrictedAPIs: string[];    // Restricted browser APIs
}
```

## Error Handling

The sandbox provides comprehensive error handling:

```typescript
// Compilation errors
interface CompileError {
  type: 'compile';
  message: string;
  file: string;
  line: number;
  column: number;
}

// Runtime errors
interface RuntimeError {
  type: 'runtime';
  message: string;
  stack: string;
}

// Dependency errors
interface DependencyError {
  type: 'dependency';
  message: string;
  package: string;
}
```

## Performance

- **Fast Compilation**: Babel transpilation typically takes <100ms
- **Memory Efficient**: Configurable memory limits and cleanup
- **CDN Optimization**: Packages loaded from fast CDNs (ESM.sh, Skypack)
- **Hot Reload**: Incremental updates for fast development

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Dependencies

- React 18+
- Babel Standalone
- CodeMirror 6
- Zustand (for state management)
- Radix UI components

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## License

MIT License - see LICENSE file for details.
