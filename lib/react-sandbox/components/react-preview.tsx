'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { 
  Play, 
  Square, 
  RefreshCw, 
  AlertCircle, 
  Loader2,
  Maximize2,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  ReactProject, 
  SandboxExecution, 
  SandboxError, 
  SandboxLog,
  ReactPreviewProps 
} from '../types';
import { ReactSandboxRuntime } from '../core/sandbox-runtime';

export function ReactPreview({
  project,
  className,
  onError,
  onLog,
  onStatusChange
}: ReactPreviewProps) {
  const previewRef = useRef<HTMLDivElement>(null);
  const runtimeRef = useRef<ReactSandboxRuntime | null>(null);
  const [execution, setExecution] = useState<SandboxExecution | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Initialize runtime
  useEffect(() => {
    if (!runtimeRef.current) {
      runtimeRef.current = new ReactSandboxRuntime({
        enableConsole: true,
        enableNetworking: true,
        maxMemory: 50 * 1024 * 1024 // 50MB
      });

      // Listen to runtime events
      runtimeRef.current.addEventListener((event) => {
        switch (event.type) {
          case 'console-log':
            onLog?.(event.log);
            break;
          case 'runtime-error':
          case 'build-error':
            onError?.(event.error);
            break;
        }
      });
    }

    return () => {
      if (runtimeRef.current) {
        runtimeRef.current.cleanup();
      }
    };
  }, [onError, onLog]);

  // Update status when execution changes
  useEffect(() => {
    if (execution) {
      onStatusChange?.(execution.status);
    }
  }, [execution?.status, onStatusChange]);

  const runProject = useCallback(async () => {
    if (!runtimeRef.current || !previewRef.current) return;

    try {
      // Stop existing execution
      if (execution) {
        await runtimeRef.current.stopExecution(project.id);
      }

      // Clear preview
      previewRef.current.innerHTML = '';

      // Run new execution
      const newExecution = await runtimeRef.current.runProject(project, previewRef.current);
      setExecution(newExecution);
    } catch (error: any) {
      const sandboxError: SandboxError = {
        type: 'runtime',
        message: error.message,
        stack: error.stack
      };
      onError?.(sandboxError);
    }
  }, [project, execution, onError]);

  const stopProject = useCallback(async () => {
    if (!runtimeRef.current || !execution) return;

    await runtimeRef.current.stopExecution(project.id);
    setExecution(null);
    
    if (previewRef.current) {
      previewRef.current.innerHTML = '';
    }
  }, [project.id, execution]);

  const refreshProject = useCallback(async () => {
    await stopProject();
    setTimeout(runProject, 100);
  }, [stopProject, runProject]);

  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(!isFullscreen);
  }, [isFullscreen]);

  const getStatusColor = (status: SandboxExecution['status']) => {
    switch (status) {
      case 'building':
        return 'bg-yellow-500';
      case 'running':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
      case 'stopped':
        return 'bg-gray-500';
      default:
        return 'bg-gray-400';
    }
  };

  const getStatusText = (status: SandboxExecution['status']) => {
    switch (status) {
      case 'building':
        return 'Building...';
      case 'running':
        return 'Running';
      case 'error':
        return 'Error';
      case 'stopped':
        return 'Stopped';
      default:
        return 'Idle';
    }
  };

  return (
    <div className={cn(
      'flex flex-col h-full bg-background border rounded-lg overflow-hidden',
      isFullscreen && 'fixed inset-0 z-50',
      className
    )}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b bg-muted/30">
        <div className="flex items-center gap-3">
          <h3 className="text-sm font-medium">{project.name}</h3>
          
          {execution && (
            <Badge 
              variant="outline" 
              className={cn('text-xs', getStatusColor(execution.status))}
            >
              <div className={cn('w-2 h-2 rounded-full mr-1', getStatusColor(execution.status))} />
              {getStatusText(execution.status)}
            </Badge>
          )}

          {execution?.buildTime && (
            <span className="text-xs text-muted-foreground">
              Built in {execution.buildTime}ms
            </span>
          )}
        </div>

        <div className="flex items-center gap-1">
          {execution?.status === 'running' ? (
            <Button
              variant="outline"
              size="sm"
              onClick={stopProject}
              className="h-7"
            >
              <Square className="h-3 w-3 mr-1" />
              Stop
            </Button>
          ) : (
            <Button
              variant="outline"
              size="sm"
              onClick={runProject}
              disabled={execution?.status === 'building'}
              className="h-7"
            >
              {execution?.status === 'building' ? (
                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
              ) : (
                <Play className="h-3 w-3 mr-1" />
              )}
              {execution?.status === 'building' ? 'Building' : 'Run'}
            </Button>
          )}

          <Button
            variant="outline"
            size="sm"
            onClick={refreshProject}
            disabled={execution?.status === 'building'}
            className="h-7"
          >
            <RefreshCw className="h-3 w-3" />
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={toggleFullscreen}
            className="h-7"
          >
            <Maximize2 className="h-3 w-3" />
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="h-7"
          >
            <Settings className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {execution?.error && (
        <Alert variant="destructive" className="m-3">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="font-medium">{execution.error.message}</div>
            {execution.error.file && (
              <div className="text-xs mt-1">
                {execution.error.file}
                {execution.error.line && `:${execution.error.line}`}
                {execution.error.column && `:${execution.error.column}`}
              </div>
            )}
          </AlertDescription>
        </Alert>
      )}

      {/* Preview Area */}
      <div className="flex-1 relative overflow-hidden">
        {execution?.status === 'building' && (
          <div className="absolute inset-0 flex items-center justify-center bg-background/80 z-10">
            <div className="flex items-center gap-2">
              <Loader2 className="h-5 w-5 animate-spin" />
              <span className="text-sm">Building project...</span>
            </div>
          </div>
        )}

        <div
          ref={previewRef}
          className="w-full h-full bg-white"
          style={{
            // Ensure the preview has a clean environment
            isolation: 'isolate'
          }}
        />

        {!execution && (
          <div className="absolute inset-0 flex items-center justify-center text-muted-foreground">
            <div className="text-center">
              <Play className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-sm">Click &quot;Run&quot; to preview your React app</p>
            </div>
          </div>
        )}
      </div>

      {/* Footer with stats */}
      {execution && execution.status === 'running' && (
        <div className="flex items-center justify-between px-3 py-2 border-t bg-muted/30 text-xs text-muted-foreground">
          <div className="flex items-center gap-4">
            <span>Logs: {execution.logs.length}</span>
            {execution.memoryUsage && (
              <span>Memory: {Math.round(execution.memoryUsage / 1024 / 1024)}MB</span>
            )}
          </div>
          
          {execution.startTime && (
            <span>
              Started {execution.startTime.toLocaleTimeString()}
            </span>
          )}
        </div>
      )}
    </div>
  );
}
