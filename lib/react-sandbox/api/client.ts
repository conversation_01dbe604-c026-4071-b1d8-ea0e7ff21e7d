import { ReactProject, TranspileOptions, PackageInfo } from '../types';

/**
 * API client for React Sandbox server endpoints
 */
export class ReactSandboxAPIClient {
  private baseUrl: string;

  constructor(baseUrl = '/api/react-sandbox') {
    this.baseUrl = baseUrl;
  }

  /**
   * Project Management
   */
  async getProjects(): Promise<ReactProject[]> {
    const response = await fetch(`${this.baseUrl}/projects`);
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to fetch projects');
    }
    
    return data.data;
  }

  async getTemplates() {
    const response = await fetch(`${this.baseUrl}/projects?type=templates`);
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to fetch templates');
    }
    
    return data.data;
  }

  async createProject(projectData: {
    name: string;
    templateId?: string;
    description?: string;
    settings?: Partial<ReactProject['settings']>;
  }): Promise<ReactProject> {
    const response = await fetch(`${this.baseUrl}/projects`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(projectData)
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to create project');
    }
    
    return data.data;
  }

  async updateProject(project: ReactProject): Promise<ReactProject> {
    const response = await fetch(`${this.baseUrl}/projects`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(project)
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to update project');
    }
    
    return data.data;
  }

  async deleteProject(projectId: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/projects?id=${projectId}`, {
      method: 'DELETE'
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to delete project');
    }
  }

  async getProject(projectId: string): Promise<ReactProject> {
    const response = await fetch(`${this.baseUrl}/projects/${projectId}`);
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to fetch project');
    }
    
    return data.data;
  }

  async updateFile(projectId: string, path: string, content: string): Promise<ReactProject> {
    const response = await fetch(`${this.baseUrl}/projects/${projectId}`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'updateFile',
        path,
        content
      })
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to update file');
    }
    
    return data.data;
  }

  async deleteFile(projectId: string, path: string): Promise<ReactProject> {
    const response = await fetch(`${this.baseUrl}/projects/${projectId}`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'deleteFile',
        path
      })
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to delete file');
    }
    
    return data.data;
  }

  async addDependency(projectId: string, name: string, version = 'latest'): Promise<ReactProject> {
    const response = await fetch(`${this.baseUrl}/projects/${projectId}`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'addDependency',
        name,
        version
      })
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to add dependency');
    }
    
    return data.data;
  }

  async removeDependency(projectId: string, name: string): Promise<ReactProject> {
    const response = await fetch(`${this.baseUrl}/projects/${projectId}`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'removeDependency',
        name
      })
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to remove dependency');
    }
    
    return data.data;
  }

  /**
   * Code Transpilation
   */
  async transpileProject(files: Record<string, string>, options?: TranspileOptions) {
    const response = await fetch(`${this.baseUrl}/transpile`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ files, options })
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Transpilation failed');
    }
    
    return data.data;
  }

  async transpileFile(filename: string, code: string, options?: TranspileOptions) {
    const response = await fetch(`${this.baseUrl}/transpile?mode=file`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ filename, code, options })
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'File transpilation failed');
    }
    
    return data.data;
  }

  async clearTranspileCache() {
    const response = await fetch(`${this.baseUrl}/transpile/cache?action=clear`);
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to clear cache');
    }
    
    return data;
  }

  /**
   * Package Management
   */
  async searchPackages(query: string, limit = 10): Promise<PackageInfo[]> {
    const response = await fetch(
      `${this.baseUrl}/packages?action=search&query=${encodeURIComponent(query)}&limit=${limit}`
    );
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Package search failed');
    }
    
    return data.data;
  }

  async getPackageInfo(name: string): Promise<PackageInfo> {
    const response = await fetch(
      `${this.baseUrl}/packages?action=info&name=${encodeURIComponent(name)}`
    );
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to get package info');
    }
    
    return data.data;
  }

  async resolvePackage(name: string, version = 'latest', provider: 'esm.sh' | 'skypack' | 'unpkg' = 'esm.sh') {
    const response = await fetch(`${this.baseUrl}/packages`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ name, version, provider })
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Package resolution failed');
    }
    
    return data.data;
  }

  /**
   * Code Execution
   */
  async executeProject(project: ReactProject) {
    const response = await fetch(`${this.baseUrl}/execute`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        projectId: project.id,
        files: project.files,
        dependencies: project.dependencies,
        entryPoint: project.entryPoint,
        options: {
          typescript: project.settings.typescript,
          jsx: project.settings.jsx,
          target: project.settings.target,
          development: true
        }
      })
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Code execution failed');
    }
    
    return data.data;
  }

  async validateCode(code: string, filename: string, options?: { typescript?: boolean; jsx?: boolean }) {
    const response = await fetch(`${this.baseUrl}/execute?action=validate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ code, filename, options })
    });
    
    const data = await response.json();
    
    return data.data;
  }

  /**
   * Performance Monitoring
   */
  async recordPerformanceMetric(
    projectId: string, 
    type: 'build' | 'render' | 'memory' | 'error' | 'warning',
    value: number,
    metadata?: Record<string, any>
  ) {
    const response = await fetch(`${this.baseUrl}/performance`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        projectId,
        type,
        value,
        metadata,
        timestamp: new Date().toISOString()
      })
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to record performance metric');
    }
    
    return data.data;
  }

  async getPerformanceSummary(projectId: string) {
    const response = await fetch(`${this.baseUrl}/performance?action=summary&projectId=${projectId}`);
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to get performance summary');
    }
    
    return data.data;
  }

  /**
   * Health Check
   */
  async healthCheck() {
    const response = await fetch(`${this.baseUrl}/health`);
    return response.json();
  }

  async detailedHealthCheck(tests: string[] = []) {
    const response = await fetch(`${this.baseUrl}/health`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ tests })
    });
    
    return response.json();
  }
}

// Export singleton instance
export const reactSandboxAPI = new ReactSandboxAPIClient();
