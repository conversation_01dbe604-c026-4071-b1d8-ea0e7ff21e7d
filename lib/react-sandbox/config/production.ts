import { SandboxConfig } from '../types';

/**
 * Production configuration for React Sandbox
 */
export const productionConfig: SandboxConfig = {
  // Security settings
  allowedDomains: [
    'cdn.skypack.dev',
    'esm.sh',
    'unpkg.com',
    'cdn.jsdelivr.net',
    'registry.npmjs.org'
  ],
  
  // Resource limits
  maxMemory: 100 * 1024 * 1024, // 100MB
  timeout: 30000, // 30 seconds
  
  // Feature flags
  enableNetworking: true,
  enableFileSystem: false, // Disabled for security
  enableConsole: true,
  enableDebugger: false, // Disabled in production
  
  // Restricted APIs for security
  restrictedAPIs: [
    'eval',
    'Function',
    'localStorage',
    'sessionStorage',
    'indexedDB',
    'webkitRequestFileSystem',
    'webkitResolveLocalFileSystemURL',
    'Worker',
    'SharedWorker',
    'ServiceWorker',
    'navigator.geolocation',
    'navigator.camera',
    'navigator.microphone',
    'WebRTC',
    'XMLHttpRequest',
    'fetch' // Controlled through allowedDomains
  ]
};

/**
 * Development configuration for React Sandbox
 */
export const developmentConfig: SandboxConfig = {
  // More permissive security settings for development
  allowedDomains: ['*'],
  
  // Higher resource limits for development
  maxMemory: 200 * 1024 * 1024, // 200MB
  timeout: 60000, // 60 seconds
  
  // All features enabled
  enableNetworking: true,
  enableFileSystem: true,
  enableConsole: true,
  enableDebugger: true,
  
  // Fewer restrictions in development
  restrictedAPIs: [
    'eval',
    'Function'
  ]
};

/**
 * Get configuration based on environment
 */
export function getSandboxConfig(): SandboxConfig {
  const isProduction = process.env.NODE_ENV === 'production';
  return isProduction ? productionConfig : developmentConfig;
}

/**
 * Babel configuration for production
 */
export const productionBabelConfig = {
  presets: [
    ['@babel/preset-env', {
      targets: {
        browsers: ['last 2 versions', 'not dead', '> 0.2%']
      },
      modules: false,
      useBuiltIns: 'entry',
      corejs: 3
    }],
    ['@babel/preset-react', {
      runtime: 'automatic',
      development: false
    }],
    ['@babel/preset-typescript', {
      allowNamespaces: true,
      allowDeclareFields: true
    }]
  ],
  plugins: [
    '@babel/plugin-transform-class-properties',
    '@babel/plugin-transform-object-rest-spread',
    '@babel/plugin-transform-runtime'
  ],
  compact: true,
  minified: true
};

/**
 * Babel configuration for development
 */
export const developmentBabelConfig = {
  presets: [
    ['@babel/preset-env', {
      targets: {
        browsers: ['last 2 versions']
      },
      modules: false
    }],
    ['@babel/preset-react', {
      runtime: 'automatic',
      development: true
    }],
    ['@babel/preset-typescript', {
      allowNamespaces: true,
      allowDeclareFields: true
    }]
  ],
  plugins: [
    '@babel/plugin-transform-class-properties',
    '@babel/plugin-transform-object-rest-spread',
    '@babel/plugin-transform-runtime'
  ],
  compact: false,
  retainLines: true
};

/**
 * CDN configuration for package resolution
 */
export const cdnConfig = {
  primary: 'esm.sh',
  fallbacks: ['skypack', 'unpkg'],
  timeout: 10000,
  retries: 3,
  cache: {
    enabled: true,
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    maxSize: 50 * 1024 * 1024 // 50MB
  }
};

/**
 * Performance monitoring configuration
 */
export const performanceConfig = {
  enabled: true,
  sampleRate: 1.0, // 100% sampling in development, reduce in production
  maxMetricsAge: 60 * 60 * 1000, // 1 hour
  thresholds: {
    buildTime: 5000, // 5 seconds
    bundleSize: 2 * 1024 * 1024, // 2MB
    memoryUsage: 100 * 1024 * 1024, // 100MB
    renderTime: 1000 // 1 second
  }
};

/**
 * Error reporting configuration
 */
export const errorReportingConfig = {
  enabled: process.env.NODE_ENV === 'production',
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV || 'development',
  sampleRate: 1.0,
  beforeSend: (event: any) => {
    // Filter out sensitive information
    if (event.exception) {
      event.exception.values?.forEach((exception: any) => {
        if (exception.stacktrace?.frames) {
          exception.stacktrace.frames = exception.stacktrace.frames.filter(
            (frame: any) => !frame.filename?.includes('node_modules')
          );
        }
      });
    }
    return event;
  }
};

/**
 * Feature flags for gradual rollout
 */
export const featureFlags = {
  enableMonacoEditor: false, // Use CodeMirror by default
  enableAdvancedDebugging: false,
  enableCollaboration: false,
  enableCloudSave: false,
  enableAIAssistant: false,
  enablePerformanceProfiler: true,
  enableHotReload: true,
  enableSourceMaps: true
};

/**
 * Security headers for iframe sandboxing
 */
export const securityHeaders = {
  'Content-Security-Policy': [
    "default-src 'self'",
    "script-src 'self' 'unsafe-eval' 'unsafe-inline' cdn.skypack.dev esm.sh unpkg.com",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: blob:",
    "font-src 'self' data:",
    "connect-src 'self' cdn.skypack.dev esm.sh unpkg.com registry.npmjs.org",
    "frame-src 'none'",
    "object-src 'none'",
    "base-uri 'self'"
  ].join('; '),
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'Referrer-Policy': 'strict-origin-when-cross-origin'
};

/**
 * Rate limiting configuration
 */
export const rateLimitConfig = {
  buildRequests: {
    windowMs: 60 * 1000, // 1 minute
    max: 30 // 30 builds per minute
  },
  packageInstalls: {
    windowMs: 60 * 1000, // 1 minute
    max: 10 // 10 package installs per minute
  },
  codeExecution: {
    windowMs: 60 * 1000, // 1 minute
    max: 100 // 100 executions per minute
  }
};
