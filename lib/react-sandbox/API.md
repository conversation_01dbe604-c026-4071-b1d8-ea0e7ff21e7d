# React Sandbox API Documentation

## Overview

The React Sandbox API provides server-side endpoints for managing React projects, transpiling code, managing packages, and monitoring performance. All endpoints return JSON responses with a consistent structure.

## Base URL

```
/api/react-sandbox
```

## Response Format

All API responses follow this structure:

```json
{
  "success": boolean,
  "data": any,
  "error": string,
  "message": string
}
```

## Authentication

Currently, the API does not require authentication. In production, implement proper authentication and authorization.

## Endpoints

### Projects

#### GET /api/react-sandbox/projects
Get all projects or templates.

**Query Parameters:**
- `type` (optional): Set to "templates" to get available templates

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "project_123",
      "name": "My React App",
      "description": "A sample React application",
      "files": { "App.jsx": "...", "index.js": "..." },
      "dependencies": { "react": "^18.0.0" },
      "settings": { "typescript": false, "jsx": true }
    }
  ]
}
```

#### POST /api/react-sandbox/projects
Create a new project.

**Request Body:**
```json
{
  "name": "My New Project",
  "templateId": "basic-react",
  "description": "Optional description",
  "settings": {
    "typescript": false,
    "jsx": true,
    "hotReload": true
  }
}
```

#### PUT /api/react-sandbox/projects
Update an existing project.

**Request Body:**
```json
{
  "id": "project_123",
  "name": "Updated Name",
  "files": { "App.jsx": "updated content" },
  "dependencies": { "react": "^18.0.0", "lodash": "^4.17.21" }
}
```

#### DELETE /api/react-sandbox/projects?id={projectId}
Delete a project.

### Individual Project Operations

#### GET /api/react-sandbox/projects/{id}
Get a specific project by ID.

#### PATCH /api/react-sandbox/projects/{id}
Update specific parts of a project.

**Request Body Examples:**

Update a file:
```json
{
  "action": "updateFile",
  "path": "App.jsx",
  "content": "import React from 'react';\n..."
}
```

Delete a file:
```json
{
  "action": "deleteFile",
  "path": "unused.js"
}
```

Add dependency:
```json
{
  "action": "addDependency",
  "name": "lodash",
  "version": "^4.17.21"
}
```

Remove dependency:
```json
{
  "action": "removeDependency",
  "name": "lodash"
}
```

Update settings:
```json
{
  "action": "updateSettings",
  "settings": {
    "typescript": true,
    "target": "es2020"
  }
}
```

### Code Transpilation

#### POST /api/react-sandbox/transpile
Transpile code files.

**Query Parameters:**
- `mode` (optional): "project" (default) or "file"

**Request Body (Project Mode):**
```json
{
  "files": {
    "App.jsx": "const App = () => <div>Hello</div>;",
    "index.js": "import React from 'react';"
  },
  "options": {
    "typescript": false,
    "jsx": true,
    "target": "es2020",
    "sourceMaps": true,
    "minify": false
  }
}
```

**Request Body (File Mode):**
```json
{
  "filename": "App.jsx",
  "code": "const App = () => <div>Hello</div>;",
  "options": {
    "jsx": true,
    "sourceMaps": true
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "code": "transpiled code...",
    "dependencies": ["react"],
    "errors": [],
    "warnings": [],
    "buildTime": 150
  }
}
```

#### GET /api/react-sandbox/transpile/cache?action={action}
Manage transpiler cache.

**Actions:**
- `stats`: Get cache statistics
- `clear`: Clear the cache

### Package Management

#### GET /api/react-sandbox/packages?action={action}
Search packages or get package information.

**Search Packages:**
```
GET /api/react-sandbox/packages?action=search&query=react&limit=10
```

**Get Package Info:**
```
GET /api/react-sandbox/packages?action=info&name=react
```

**Response (Search):**
```json
{
  "success": true,
  "data": [
    {
      "name": "react",
      "version": "18.2.0",
      "description": "React is a JavaScript library...",
      "keywords": ["react", "ui"],
      "author": "Meta"
    }
  ]
}
```

#### POST /api/react-sandbox/packages
Resolve package URL for CDN loading.

**Request Body:**
```json
{
  "name": "react",
  "version": "18.2.0",
  "provider": "esm.sh"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "name": "react",
    "version": "18.2.0",
    "url": "https://esm.sh/react@18.2.0",
    "provider": "esm.sh",
    "type": "esm"
  }
}
```

### Code Execution

#### POST /api/react-sandbox/execute
Execute or validate React code.

**Query Parameters:**
- `action`: "run" (default) or "validate"

**Request Body (Run):**
```json
{
  "projectId": "project_123",
  "files": {
    "App.jsx": "const App = () => <div>Hello</div>;",
    "index.js": "import React from 'react';"
  },
  "dependencies": {
    "react": "^18.0.0",
    "react-dom": "^18.0.0"
  },
  "entryPoint": "index.js",
  "options": {
    "typescript": false,
    "jsx": true,
    "development": true
  }
}
```

**Request Body (Validate):**
```json
{
  "code": "const App = () => <div>Hello</div>;",
  "filename": "App.jsx",
  "options": {
    "jsx": true
  }
}
```

#### GET /api/react-sandbox/execute?projectId={projectId}
Get execution result for a project.

#### DELETE /api/react-sandbox/execute?projectId={projectId}
Clear execution results (all projects if no projectId specified).

### Performance Monitoring

#### POST /api/react-sandbox/performance
Record performance metrics.

**Request Body:**
```json
{
  "projectId": "project_123",
  "type": "build",
  "value": 1500,
  "timestamp": "2024-01-01T00:00:00Z",
  "metadata": {
    "bundleSize": 50000,
    "fileCount": 5
  }
}
```

**Metric Types:**
- `build`: Build time in milliseconds
- `render`: Render time in milliseconds
- `memory`: Memory usage in bytes
- `error`: Error count (value should be 1)
- `warning`: Warning count (value should be 1)

#### GET /api/react-sandbox/performance?action={action}
Query performance metrics.

**Actions:**
- `query`: Get metrics with filters
- `summary`: Get performance summary for a project
- `stats`: Get overall statistics

**Query Parameters (for query action):**
- `projectId`: Filter by project
- `type`: Filter by metric type
- `startTime`: Start time (ISO string)
- `endTime`: End time (ISO string)
- `limit`: Maximum results (default: 100)

**Summary Example:**
```
GET /api/react-sandbox/performance?action=summary&projectId=project_123
```

**Response:**
```json
{
  "success": true,
  "data": {
    "projectId": "project_123",
    "summary": {
      "buildTime": { "avg": 1200, "min": 800, "max": 2000, "count": 10 },
      "renderTime": { "avg": 50, "min": 20, "max": 100, "count": 15 },
      "memoryUsage": { "avg": 25000000, "min": 20000000, "max": 30000000, "count": 20 },
      "errorCount": 2,
      "warningCount": 5
    }
  }
}
```

### Health Check

#### GET /api/react-sandbox/health
Basic health check.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "responseTime": 45,
  "version": "1.0.0",
  "checks": {
    "api": true,
    "transpiler": true,
    "packageManager": true,
    "externalServices": true
  },
  "system": {
    "nodeVersion": "v18.17.0",
    "platform": "linux",
    "memory": {
      "used": 150,
      "total": 200
    }
  }
}
```

#### POST /api/react-sandbox/health
Detailed health check with custom tests.

**Request Body:**
```json
{
  "tests": [
    "transpile-jsx",
    "transpile-typescript",
    "package-search",
    "cdn-resolve",
    "memory-usage"
  ]
}
```

## Error Handling

All endpoints return appropriate HTTP status codes:

- `200`: Success
- `201`: Created (for POST requests)
- `400`: Bad Request (validation errors)
- `404`: Not Found
- `500`: Internal Server Error
- `503`: Service Unavailable (health check failures)

Error responses include details:

```json
{
  "success": false,
  "error": "Validation failed",
  "details": [
    {
      "field": "name",
      "message": "Name is required"
    }
  ]
}
```

## Rate Limiting

The API implements rate limiting:

- Build requests: 30 per minute
- Package installs: 10 per minute
- Code execution: 100 per minute

Rate limit headers are included in responses:

```
X-RateLimit-Limit: 30
X-RateLimit-Remaining: 25
X-RateLimit-Reset: **********
```

## Client Usage

Use the provided API client for easy integration:

```typescript
import { reactSandboxAPI } from '@/lib/react-sandbox/api/client';

// Create a project
const project = await reactSandboxAPI.createProject({
  name: 'My App',
  templateId: 'basic-react'
});

// Update a file
await reactSandboxAPI.updateFile(project.id, 'App.jsx', newContent);

// Search packages
const packages = await reactSandboxAPI.searchPackages('react');

// Execute project
const result = await reactSandboxAPI.executeProject(project);
```
