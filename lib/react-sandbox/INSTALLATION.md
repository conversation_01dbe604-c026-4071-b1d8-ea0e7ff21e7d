# React Sandbox - Production Installation Guide

## ✅ Dependencies Status

All required dependencies have been successfully installed:

### Core Dependencies ✅
- `@babel/standalone` - For code transpilation
- `@babel/preset-env` - ES6+ support
- `@babel/preset-react` - JSX support
- `@babel/preset-typescript` - TypeScript support
- `react-error-boundary` - Error handling
- `terser` - Code minification
- `monaco-editor` - Advanced code editor (optional)
- `@sentry/react` - Error reporting
- `nanoid` - ID generation
- `lodash-es` - Utilities

### UI Dependencies ✅
- All Radix UI components
- Tailwind CSS
- Lucide React icons
- React Resizable Panels

### Development Dependencies ✅
- TypeScript types for all Babel packages
- ESLint and Prettier configurations

## 🚀 Quick Start

### 1. Basic Usage

```tsx
import { ReactSandbox, useReactSandbox } from '@/lib/react-sandbox';

function MyApp() {
  const { project, createProject, availableTemplates } = useReactSandbox();

  const handleCreate = () => {
    createProject(availableTemplates[0], 'My React App');
  };

  return project ? (
    <ReactSandbox project={project} />
  ) : (
    <button onClick={handleCreate}>Create React App</button>
  );
}
```

### 2. Production Usage

```tsx
import { ProductionReactSandbox } from '@/lib/react-sandbox';

function App() {
  return <ProductionReactSandbox />;
}
```

## 🔧 Configuration

### Environment Variables

Add to your `.env.local`:

```bash
# Optional: Sentry for error reporting
SENTRY_DSN=your_sentry_dsn_here

# Environment
NODE_ENV=production
```

### Next.js Configuration

Add to your `next.config.js`:

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable WebAssembly for esbuild-wasm
  webpack: (config) => {
    config.experiments = {
      ...config.experiments,
      asyncWebAssembly: true,
    };
    return config;
  },
  
  // Security headers for sandbox
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' cdn.skypack.dev esm.sh unpkg.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self' data:; connect-src 'self' cdn.skypack.dev esm.sh unpkg.com registry.npmjs.org;"
          }
        ]
      }
    ];
  }
};

module.exports = nextConfig;
```

## 📁 File Structure

```
lib/react-sandbox/
├── components/           # React components
│   ├── react-sandbox.tsx       # Main sandbox component
│   ├── react-preview.tsx       # Live preview
│   ├── sandbox-console.tsx     # Debug console
│   ├── dependency-manager.tsx  # Package manager UI
│   └── error-boundary.tsx      # Error handling
├── core/                # Core functionality
│   ├── transpiler.ts           # Babel transpilation
│   ├── package-manager.ts      # Dynamic package loading
│   └── sandbox-runtime.ts      # Execution environment
├── hooks/               # React hooks
│   └── use-react-sandbox.ts    # Main hook
├── utils/               # Utilities
│   ├── templates.ts            # Project templates
│   └── performance-monitor.ts  # Performance tracking
├── config/              # Configuration
│   └── production.ts           # Production settings
├── examples/            # Usage examples
│   ├── basic-sandbox.tsx       # Basic example
│   └── production-sandbox.tsx  # Production example
└── types/               # TypeScript definitions
    └── index.ts                # All type definitions
```

## 🛡️ Security Features

### Sandboxed Execution
- Code runs in isolated environment
- Restricted API access
- Memory limits enforced
- Network request filtering

### Content Security Policy
- Prevents XSS attacks
- Restricts script sources
- Controls resource loading

### Error Boundaries
- Graceful error handling
- User-friendly error messages
- Automatic error recovery

## 📊 Performance Monitoring

### Built-in Metrics
- Build time tracking
- Memory usage monitoring
- Bundle size analysis
- Performance scoring

### Integration
```tsx
import { performanceMonitor } from '@/lib/react-sandbox';

// Get metrics for a project
const metrics = performanceMonitor.getMetrics(projectId);
const summary = performanceMonitor.getPerformanceSummary(projectId);
```

## 🔍 Error Handling

### Error Boundary Usage
```tsx
import { SandboxErrorBoundary } from '@/lib/react-sandbox';

function MyComponent() {
  return (
    <SandboxErrorBoundary
      onError={(error, errorInfo) => {
        console.error('Sandbox error:', error);
      }}
    >
      <YourComponent />
    </SandboxErrorBoundary>
  );
}
```

### Error Reporting
Automatic error reporting to Sentry (if configured):
- Runtime errors
- Compilation errors
- Performance issues

## 🎯 Production Checklist

### ✅ Security
- [x] Sandboxed execution
- [x] CSP headers configured
- [x] API restrictions in place
- [x] Memory limits enforced

### ✅ Performance
- [x] Code transpilation caching
- [x] Package loading optimization
- [x] Memory monitoring
- [x] Performance metrics

### ✅ Error Handling
- [x] React Error Boundaries
- [x] Graceful degradation
- [x] User-friendly error messages
- [x] Error reporting integration

### ✅ Monitoring
- [x] Performance tracking
- [x] Memory usage monitoring
- [x] Error rate tracking
- [x] Build time optimization

## 🚀 Deployment

### Vercel (Recommended)
```bash
# Deploy with Vercel
vercel --prod
```

### Docker
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

### Environment Variables for Production
```bash
NODE_ENV=production
SENTRY_DSN=your_sentry_dsn
NEXT_PUBLIC_APP_URL=https://your-domain.com
```

## 📈 Scaling Considerations

### Performance
- Enable CDN for static assets
- Use Redis for caching (if needed)
- Monitor memory usage
- Set up performance alerts

### Security
- Regular security audits
- Update dependencies
- Monitor for vulnerabilities
- Implement rate limiting

### Monitoring
- Set up application monitoring
- Track user metrics
- Monitor error rates
- Performance dashboards

## 🆘 Troubleshooting

### Common Issues

1. **Memory Issues**
   - Increase memory limits in config
   - Clear package cache regularly
   - Monitor memory usage

2. **Build Failures**
   - Check Babel configuration
   - Verify package dependencies
   - Review error logs

3. **Security Errors**
   - Update CSP headers
   - Check allowed domains
   - Verify API restrictions

### Support
- Check the examples in `/examples`
- Review the TypeScript definitions
- Monitor console for errors
- Use the performance monitor for debugging

## 🎉 You're Ready!

Your React Sandbox is now production-ready with:
- ✅ All dependencies installed
- ✅ Security measures in place
- ✅ Performance monitoring
- ✅ Error handling
- ✅ Production configuration

Start building amazing React applications in the browser! 🚀
