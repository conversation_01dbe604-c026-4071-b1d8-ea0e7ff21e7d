// Core IDE Types and Interfaces

export interface FileNode {
  id: string;
  name: string;
  type: 'file' | 'directory';
  path: string;
  parentId?: string;
  children?: FileNode[];
  content?: string;
  size?: number;
  lastModified: Date;
  isOpen?: boolean;
  isDirty?: boolean;
}

export interface Tab {
  id: string;
  fileId: string;
  title: string;
  path: string;
  isDirty: boolean;
  isActive: boolean;
  content: string;
  language?: string;
  cursorPosition?: {
    line: number;
    column: number;
  };
}

export interface EditorState {
  activeTabId?: string;
  tabs: Tab[];
  fontSize: number;
  theme: 'light' | 'dark';
  wordWrap: boolean;
  showLineNumbers: boolean;
  showMinimap: boolean;
}

export interface AIMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  context?: {
    fileId?: string;
    selectedText?: string;
    cursorPosition?: {
      line: number;
      column: number;
    };
  };
}

export interface AIConversation {
  id: string;
  title: string;
  messages: AIMessage[];
  createdAt: Date;
  updatedAt: Date;
}

export interface FileSystemState {
  rootNode: FileNode | null;
  selectedFileId?: string;
  expandedDirectories: Set<string>;
  searchQuery: string;
  searchResults: FileNode[];
}

export interface UIState {
  sidebarVisible: boolean;
  sidebarWidth: number;
  terminalVisible: boolean;
  terminalHeight: number;
  aiPanelVisible: boolean;
  aiPanelWidth: number;
  statusBarVisible: boolean;
  layout: 'default' | 'zen' | 'focus';
}

export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  metaKey?: boolean;
  action: string;
  description: string;
}

export interface TerminalSession {
  id: string;
  title: string;
  isActive: boolean;
  history: string[];
  currentDirectory: string;
}

export interface ProjectConfig {
  name: string;
  description?: string;
  version: string;
  language: string;
  framework?: string;
  dependencies: Record<string, string>;
  scripts: Record<string, string>;
  settings: {
    autoSave: boolean;
    formatOnSave: boolean;
    linting: boolean;
    typeChecking: boolean;
  };
}

// Event types for the IDE
export type IDEEvent = 
  | { type: 'FILE_OPENED'; payload: { fileId: string } }
  | { type: 'FILE_CLOSED'; payload: { fileId: string } }
  | { type: 'FILE_SAVED'; payload: { fileId: string } }
  | { type: 'FILE_CREATED'; payload: { file: FileNode } }
  | { type: 'FILE_DELETED'; payload: { fileId: string } }
  | { type: 'FILE_RENAMED'; payload: { fileId: string; newName: string } }
  | { type: 'DIRECTORY_CREATED'; payload: { directory: FileNode } }
  | { type: 'TAB_SWITCHED'; payload: { tabId: string } }
  | { type: 'AI_MESSAGE_SENT'; payload: { message: AIMessage } }
  | { type: 'AI_RESPONSE_RECEIVED'; payload: { message: AIMessage } };

// Language support
export interface LanguageSupport {
  id: string;
  name: string;
  extensions: string[];
  mimeType: string;
  codemirrorMode?: string;
  highlighter?: string;
  linter?: string;
  formatter?: string;
}

// Plugin system types
export interface IDEPlugin {
  id: string;
  name: string;
  version: string;
  description: string;
  author: string;
  enabled: boolean;
  hooks: {
    onFileOpen?: (file: FileNode) => void;
    onFileSave?: (file: FileNode) => void;
    onTabSwitch?: (tab: Tab) => void;
    onAIMessage?: (message: AIMessage) => void;
  };
}

export interface SearchResult {
  fileId: string;
  filePath: string;
  line: number;
  column: number;
  text: string;
  context: string;
}

export interface GitStatus {
  branch: string;
  status: 'clean' | 'dirty' | 'ahead' | 'behind' | 'diverged';
  changes: {
    added: string[];
    modified: string[];
    deleted: string[];
    untracked: string[];
  };
}
