'use client';

import React, { useEffect } from 'react';
import { IDELayout } from '../components/ide-layout';
import { useFileSystemStore } from '../stores/file-system-store';

/**
 * Basic IDE Example
 * 
 * This component demonstrates how to use the IDE library with some sample files.
 * It creates a basic project structure and opens the IDE.
 */
export function BasicIDEExample() {
  const { createFile, createDirectory, getFileById } = useFileSystemStore();

  // Initialize with sample files
  useEffect(() => {
    const initializeProject = async () => {
      try {
        // Get root directory
        const root = useFileSystemStore.getState().rootNode;
        if (!root) return;

        // Create project structure
        const srcDir = createDirectory(root.id, 'src');
        const publicDir = createDirectory(root.id, 'public');
        const docsDir = createDirectory(root.id, 'docs');

        // Create sample files
        createFile(root.id, 'package.json', JSON.stringify({
          "name": "my-project",
          "version": "1.0.0",
          "description": "A sample project",
          "main": "src/index.js",
          "scripts": {
            "start": "node src/index.js",
            "test": "echo \"Error: no test specified\" && exit 1"
          },
          "dependencies": {
            "express": "^4.18.0"
          }
        }, null, 2));

        createFile(root.id, 'README.md', `# My Project

This is a sample project created with the IDE library.

## Getting Started

1. Install dependencies: \`npm install\`
2. Start the server: \`npm start\`

## Features

- Express.js server
- Modern JavaScript
- Hot reloading
- AI assistance

## Usage

Edit the files in the \`src\` directory to get started.
`);

        createFile(srcDir.id, 'index.js', `const express = require('express');
const app = express();
const port = 3000;

// Middleware
app.use(express.json());
app.use(express.static('public'));

// Routes
app.get('/', (req, res) => {
  res.json({ 
    message: 'Hello World!',
    timestamp: new Date().toISOString()
  });
});

app.get('/api/users', (req, res) => {
  res.json([
    { id: 1, name: 'John Doe', email: '<EMAIL>' },
    { id: 2, name: 'Jane Smith', email: '<EMAIL>' }
  ]);
});

app.listen(port, () => {
  console.log(\`Server running at http://localhost:\${port}\`);
});
`);

        createFile(srcDir.id, 'utils.js', `// Utility functions

export function formatDate(date) {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(date);
}

export function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

export function throttle(func, limit) {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  }
}

export function generateId() {
  return Math.random().toString(36).substr(2, 9);
}
`);

        createFile(publicDir.id, 'index.html', `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Project</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .header {
            background: #f4f4f4;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Welcome to My Project</h1>
        <p>This is a sample application built with Express.js</p>
    </div>
    
    <div id="content">
        <h2>Features</h2>
        <ul>
            <li>RESTful API</li>
            <li>Static file serving</li>
            <li>JSON responses</li>
            <li>Modern JavaScript</li>
        </ul>
        
        <button class="button" onclick="fetchUsers()">Load Users</button>
        <div id="users"></div>
    </div>

    <script>
        async function fetchUsers() {
            try {
                const response = await fetch('/api/users');
                const users = await response.json();
                
                const usersDiv = document.getElementById('users');
                usersDiv.innerHTML = '<h3>Users:</h3>' + 
                    users.map(user => 
                        \`<p><strong>\${user.name}</strong> - \${user.email}</p>\`
                    ).join('');
            } catch (error) {
                console.error('Error fetching users:', error);
            }
        }
    </script>
</body>
</html>
`);

        createFile(docsDir.id, 'API.md', `# API Documentation

## Endpoints

### GET /
Returns a welcome message with timestamp.

**Response:**
\`\`\`json
{
  "message": "Hello World!",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
\`\`\`

### GET /api/users
Returns a list of users.

**Response:**
\`\`\`json
[
  {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>"
  },
  {
    "id": 2,
    "name": "Jane Smith",
    "email": "<EMAIL>"
  }
]
\`\`\`

## Error Handling

All endpoints return appropriate HTTP status codes:
- 200: Success
- 404: Not Found
- 500: Internal Server Error
`);

        createFile(root.id, '.gitignore', `# Dependencies
node_modules/
npm-debug.log*

# Environment variables
.env
.env.local

# Build output
dist/
build/

# IDE files
.vscode/
.idea/

# OS files
.DS_Store
Thumbs.db
`);

      } catch (error) {
        console.error('Failed to initialize project:', error);
      }
    };

    // Small delay to ensure stores are ready
    setTimeout(initializeProject, 100);
  }, [createFile, createDirectory]);

  return (
    <div className="h-screen w-full">
      <IDELayout />
    </div>
  );
}
