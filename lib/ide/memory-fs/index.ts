import { FileNode } from '../types';
import { nanoid } from 'nanoid';

export class MemoryFileSystem {
  private files = new Map<string, FileNode>();
  private rootId: string;
  private listeners = new Set<(event: FileSystemEvent) => void>();

  constructor() {
    // Create root directory
    this.rootId = nanoid();
    const root: FileNode = {
      id: this.rootId,
      name: 'root',
      type: 'directory',
      path: '/',
      children: [],
      lastModified: new Date(),
    };
    this.files.set(this.rootId, root);
  }

  // Event system
  addEventListener(listener: (event: FileSystemEvent) => void) {
    this.listeners.add(listener);
  }

  removeEventListener(listener: (event: FileSystemEvent) => void) {
    this.listeners.delete(listener);
  }

  private emit(event: FileSystemEvent) {
    this.listeners.forEach(listener => listener(event));
  }

  // Core file operations
  getRoot(): FileNode {
    return this.files.get(this.rootId)!;
  }

  getFile(id: string): FileNode | undefined {
    return this.files.get(id);
  }

  getFileByPath(path: string): FileNode | undefined {
    const parts = path.split('/').filter(Boolean);
    let current = this.getRoot();

    for (const part of parts) {
      if (current.type !== 'directory' || !current.children) {
        return undefined;
      }
      const child = current.children.find(c => c.name === part);
      if (!child) return undefined;
      current = child;
    }

    return current;
  }

  createFile(parentId: string, name: string, content = ''): FileNode {
    const parent = this.files.get(parentId);
    if (!parent || parent.type !== 'directory') {
      throw new Error('Parent must be a directory');
    }

    const id = nanoid();
    const path = parent.path === '/' ? `/${name}` : `${parent.path}/${name}`;
    
    const file: FileNode = {
      id,
      name,
      type: 'file',
      path,
      parentId,
      content,
      size: content.length,
      lastModified: new Date(),
      isDirty: false,
    };

    this.files.set(id, file);
    parent.children = parent.children || [];
    parent.children.push(file);
    parent.lastModified = new Date();

    this.emit({ type: 'file-created', file });
    return file;
  }

  createDirectory(parentId: string, name: string): FileNode {
    const parent = this.files.get(parentId);
    if (!parent || parent.type !== 'directory') {
      throw new Error('Parent must be a directory');
    }

    const id = nanoid();
    const path = parent.path === '/' ? `/${name}` : `${parent.path}/${name}`;
    
    const directory: FileNode = {
      id,
      name,
      type: 'directory',
      path,
      parentId,
      children: [],
      lastModified: new Date(),
    };

    this.files.set(id, directory);
    parent.children = parent.children || [];
    parent.children.push(directory);
    parent.lastModified = new Date();

    this.emit({ type: 'directory-created', directory });
    return directory;
  }

  updateFile(id: string, content: string): void {
    const file = this.files.get(id);
    if (!file || file.type !== 'file') {
      throw new Error('File not found');
    }

    file.content = content;
    file.size = content.length;
    file.lastModified = new Date();
    file.isDirty = true;

    this.emit({ type: 'file-updated', file });
  }

  deleteFile(id: string): void {
    const file = this.files.get(id);
    if (!file) {
      throw new Error('File not found');
    }

    // Remove from parent's children
    if (file.parentId) {
      const parent = this.files.get(file.parentId);
      if (parent && parent.children) {
        parent.children = parent.children.filter(c => c.id !== id);
        parent.lastModified = new Date();
      }
    }

    // Recursively delete children if directory
    if (file.type === 'directory' && file.children) {
      file.children.forEach(child => this.deleteFile(child.id));
    }

    this.files.delete(id);
    this.emit({ type: 'file-deleted', fileId: id });
  }

  renameFile(id: string, newName: string): void {
    const file = this.files.get(id);
    if (!file) {
      throw new Error('File not found');
    }

    const oldPath = file.path;
    file.name = newName;
    
    // Update path
    const pathParts = file.path.split('/');
    pathParts[pathParts.length - 1] = newName;
    file.path = pathParts.join('/');
    file.lastModified = new Date();

    // Update children paths if directory
    if (file.type === 'directory') {
      this.updateChildrenPaths(file, oldPath);
    }

    this.emit({ type: 'file-renamed', file, oldName: oldPath });
  }

  private updateChildrenPaths(directory: FileNode, oldPath: string): void {
    if (!directory.children) return;

    directory.children.forEach(child => {
      child.path = child.path.replace(oldPath, directory.path);
      if (child.type === 'directory') {
        this.updateChildrenPaths(child, oldPath);
      }
    });
  }

  // Search functionality
  searchFiles(query: string): FileNode[] {
    const results: FileNode[] = [];
    const searchRecursive = (node: FileNode) => {
      if (node.name.toLowerCase().includes(query.toLowerCase())) {
        results.push(node);
      }
      if (node.children) {
        node.children.forEach(searchRecursive);
      }
    };

    searchRecursive(this.getRoot());
    return results;
  }

  // Import/Export functionality
  exportToJSON(): string {
    return JSON.stringify({
      root: this.getRoot(),
      files: Array.from(this.files.entries()),
    }, null, 2);
  }

  importFromJSON(json: string): void {
    try {
      const data = JSON.parse(json);
      this.files.clear();
      
      // Restore files map
      data.files.forEach(([id, file]: [string, FileNode]) => {
        this.files.set(id, file);
      });

      // Update root reference
      this.rootId = data.root.id;
      
      this.emit({ type: 'filesystem-imported' });
    } catch (error) {
      throw new Error('Invalid JSON format');
    }
  }

  // Utility methods
  getFileTree(): FileNode {
    return this.getRoot();
  }

  getAllFiles(): FileNode[] {
    return Array.from(this.files.values()).filter(f => f.type === 'file');
  }

  getFileCount(): number {
    return Array.from(this.files.values()).filter(f => f.type === 'file').length;
  }

  getTotalSize(): number {
    return this.getAllFiles().reduce((total, file) => total + (file.size || 0), 0);
  }
}

// Event types for file system
export type FileSystemEvent = 
  | { type: 'file-created'; file: FileNode }
  | { type: 'directory-created'; directory: FileNode }
  | { type: 'file-updated'; file: FileNode }
  | { type: 'file-deleted'; fileId: string }
  | { type: 'file-renamed'; file: FileNode; oldName: string }
  | { type: 'filesystem-imported' };

// Create singleton instance
export const memoryFS = new MemoryFileSystem();
