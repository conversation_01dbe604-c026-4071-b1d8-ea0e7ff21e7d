import { useCallback } from 'react';
import { useAIStore } from '../stores/ai-store';
import { useTabStore } from '../stores/tab-store';
import { useFileSystemStore } from '../stores/file-system-store';
import { AIMessage } from '../types';

export function useAIIntegration() {
  const {
    conversations,
    activeConversationId,
    isLoading,
    isConnected,
    model,
    temperature,
    maxTokens,
    createConversation,
    deleteConversation,
    setActiveConversation,
    sendMessage,
    addMessage,
    clearConversation,
    getActiveConversation,
    getConversationById,
    generateCode,
    explainCode,
    refactorCode,
    findBugs,
    generateTests,
    setModel,
    setTemperature,
    setMaxTokens,
  } = useAIStore();

  const { getActiveTab } = useTabStore();
  const { getFileById } = useFileSystemStore();

  // Enhanced AI operations with context
  const sendMessageWithContext = useCallback(async (content: string, includeFileContext = true) => {
    let context: AIMessage['context'] | undefined;

    if (includeFileContext) {
      const activeTab = getActiveTab();
      if (activeTab) {
        const file = getFileById(activeTab.fileId);
        context = {
          fileId: activeTab.fileId,
          cursorPosition: activeTab.cursorPosition,
        };
      }
    }

    await sendMessage(content, context);
  }, [sendMessage, getActiveTab, getFileById]);

  const explainSelectedCode = useCallback(async (selectedText: string) => {
    const activeTab = getActiveTab();
    if (!activeTab) return;

    const context: AIMessage['context'] = {
      fileId: activeTab.fileId,
      selectedText,
      cursorPosition: activeTab.cursorPosition,
    };

    await sendMessage(`Please explain this code: ${selectedText}`, context);
  }, [sendMessage, getActiveTab]);

  const generateCodeWithPrompt = useCallback(async (prompt: string, language?: string) => {
    const activeTab = getActiveTab();
    const targetLanguage = language || activeTab?.language || 'javascript';

    try {
      const generatedCode = await generateCode(prompt, targetLanguage);
      
      // Add the generated code as a message in the conversation
      const conversationId = activeConversationId || createConversation('Code Generation').id;
      
      addMessage(conversationId, {
        role: 'assistant',
        content: `Here's the generated ${targetLanguage} code:\n\n\`\`\`${targetLanguage}\n${generatedCode}\n\`\`\``,
      });

      return generatedCode;
    } catch (error) {
      console.error('Failed to generate code:', error);
      throw error;
    }
  }, [generateCode, activeConversationId, createConversation, addMessage, getActiveTab]);

  const refactorSelectedCode = useCallback(async (selectedText: string, instructions: string) => {
    const activeTab = getActiveTab();
    if (!activeTab) return;

    try {
      const refactoredCode = await refactorCode(selectedText, instructions, activeTab.language);
      
      const context: AIMessage['context'] = {
        fileId: activeTab.fileId,
        selectedText,
        cursorPosition: activeTab.cursorPosition,
      };

      await sendMessage(
        `I've refactored your code based on: "${instructions}"\n\nOriginal:\n\`\`\`${activeTab.language}\n${selectedText}\n\`\`\`\n\nRefactored:\n\`\`\`${activeTab.language}\n${refactoredCode}\n\`\`\``,
        context
      );

      return refactoredCode;
    } catch (error) {
      console.error('Failed to refactor code:', error);
      throw error;
    }
  }, [refactorCode, sendMessage, getActiveTab]);

  const findBugsInCode = useCallback(async (code?: string) => {
    const activeTab = getActiveTab();
    if (!activeTab && !code) return;

    const codeToAnalyze = code || activeTab?.content || '';
    const language = activeTab?.language || 'javascript';

    try {
      const bugReport = await findBugs(codeToAnalyze, language);
      
      const context: AIMessage['context'] = {
        fileId: activeTab?.fileId,
        cursorPosition: activeTab?.cursorPosition,
      };

      await sendMessage(
        `Bug analysis for your ${language} code:\n\n${bugReport}`,
        context
      );

      return bugReport;
    } catch (error) {
      console.error('Failed to find bugs:', error);
      throw error;
    }
  }, [findBugs, sendMessage, getActiveTab]);

  const generateTestsForCode = useCallback(async (code?: string) => {
    const activeTab = getActiveTab();
    if (!activeTab && !code) return;

    const codeToTest = code || activeTab?.content || '';
    const language = activeTab?.language || 'javascript';

    try {
      const tests = await generateTests(codeToTest, language);
      
      const context: AIMessage['context'] = {
        fileId: activeTab?.fileId,
        cursorPosition: activeTab?.cursorPosition,
      };

      await sendMessage(
        `Generated tests for your ${language} code:\n\n\`\`\`${language}\n${tests}\n\`\`\``,
        context
      );

      return tests;
    } catch (error) {
      console.error('Failed to generate tests:', error);
      throw error;
    }
  }, [generateTests, sendMessage, getActiveTab]);

  // Conversation management
  const startNewConversation = useCallback((title?: string) => {
    const conversation = createConversation(title);
    setActiveConversation(conversation.id);
    return conversation;
  }, [createConversation, setActiveConversation]);

  const switchConversation = useCallback((conversationId: string) => {
    setActiveConversation(conversationId);
  }, [setActiveConversation]);

  const deleteConversationWithConfirmation = useCallback((conversationId: string) => {
    const conversation = getConversationById(conversationId);
    if (!conversation) return;

    const shouldDelete = window.confirm(`Delete conversation "${conversation.title}"?`);
    if (shouldDelete) {
      deleteConversation(conversationId);
    }
  }, [deleteConversation, getConversationById]);

  // Quick actions
  const quickActions = {
    explainFile: useCallback(async () => {
      const activeTab = getActiveTab();
      if (!activeTab) return;

      await sendMessageWithContext(`Please explain what this ${activeTab.language || 'code'} file does.`);
    }, [sendMessageWithContext, getActiveTab]),

    optimizeCode: useCallback(async () => {
      const activeTab = getActiveTab();
      if (!activeTab) return;

      await sendMessageWithContext('Please suggest optimizations for this code.');
    }, [sendMessageWithContext, getActiveTab]),

    addComments: useCallback(async () => {
      const activeTab = getActiveTab();
      if (!activeTab) return;

      await sendMessageWithContext('Please add helpful comments to this code.');
    }, [sendMessageWithContext, getActiveTab]),

    convertLanguage: useCallback(async (targetLanguage: string) => {
      const activeTab = getActiveTab();
      if (!activeTab) return;

      await sendMessageWithContext(`Please convert this code to ${targetLanguage}.`);
    }, [sendMessageWithContext, getActiveTab]),

    createDocumentation: useCallback(async () => {
      const activeTab = getActiveTab();
      if (!activeTab) return;

      await sendMessageWithContext('Please create documentation for this code.');
    }, [sendMessageWithContext, getActiveTab]),
  };

  // Settings helpers
  const updateSettings = useCallback((settings: {
    model?: string;
    temperature?: number;
    maxTokens?: number;
  }) => {
    if (settings.model) setModel(settings.model);
    if (settings.temperature !== undefined) setTemperature(settings.temperature);
    if (settings.maxTokens !== undefined) setMaxTokens(settings.maxTokens);
  }, [setModel, setTemperature, setMaxTokens]);

  // Utility functions
  const getConversationCount = useCallback(() => conversations.length, [conversations]);

  const getActiveConversationMessages = useCallback(() => {
    const conversation = getActiveConversation();
    return conversation?.messages || [];
  }, [getActiveConversation]);

  const hasActiveConversation = useCallback(() => {
    return !!activeConversationId && !!getActiveConversation();
  }, [activeConversationId, getActiveConversation]);

  return {
    // State
    conversations,
    activeConversationId,
    isLoading,
    isConnected,
    model,
    temperature,
    maxTokens,

    // Basic operations
    sendMessage: sendMessageWithContext,
    createConversation: startNewConversation,
    switchConversation,
    deleteConversation: deleteConversationWithConfirmation,
    clearConversation,

    // AI-powered code operations
    explainSelectedCode,
    generateCodeWithPrompt,
    refactorSelectedCode,
    findBugsInCode,
    generateTestsForCode,

    // Quick actions
    quickActions,

    // Settings
    updateSettings,

    // Utility
    getActiveConversation,
    getConversationById,
    getConversationCount,
    getActiveConversationMessages,
    hasActiveConversation,
  };
}
