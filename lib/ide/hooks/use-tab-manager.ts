import { useCallback } from 'react';
import { useTabStore } from '../stores/tab-store';
import { useFileSystemStore } from '../stores/file-system-store';
import { Tab } from '../types';

export function useTabManager() {
  const {
    activeTabId,
    tabs,
    fontSize,
    theme,
    wordWrap,
    showLineNumbers,
    showMinimap,
    openTab,
    closeTab,
    closeAllTabs,
    closeOtherTabs,
    setActiveTab,
    updateTabContent,
    markTabDirty,
    saveTab,
    saveAllTabs,
    moveTab,
    duplicateTab,
    getTabById,
    getTabByFileId,
    getActiveTab,
    setFontSize,
    setTheme,
    toggleWordWrap,
    toggleLineNumbers,
    toggleMinimap,
    updateCursorPosition,
  } = useTabStore();

  const { updateFileContent, getFileById } = useFileSystemStore();

  // Enhanced tab operations
  const openFileInTab = useCallback((fileId: string) => {
    const file = getFileById(fileId);
    if (!file || file.type !== 'file') return;

    // Determine language from file extension
    const extension = file.name.split('.').pop()?.toLowerCase();
    const language = getLanguageFromExtension(extension);

    openTab(fileId, file.name, file.path, file.content || '', language);
  }, [openTab, getFileById]);

  const closeTabWithConfirmation = useCallback(async (tabId: string) => {
    const tab = getTabById(tabId);
    if (!tab) return;

    if (tab.isDirty) {
      // In a real implementation, you'd show a confirmation dialog
      const shouldSave = window.confirm(`Save changes to ${tab.title}?`);
      if (shouldSave) {
        await saveTabContent(tabId);
      }
    }

    closeTab(tabId);
  }, [getTabById, closeTab]);

  const saveTabContent = useCallback(async (tabId: string) => {
    const tab = getTabById(tabId);
    if (!tab) return;

    try {
      // Update file content in the file system
      updateFileContent(tab.fileId, tab.content);
      
      // Mark tab as saved
      saveTab(tabId);
    } catch (error) {
      console.error('Failed to save tab content:', error);
      throw error;
    }
  }, [getTabById, updateFileContent, saveTab]);

  const saveAllTabsContent = useCallback(async () => {
    const dirtyTabs = tabs.filter(tab => tab.isDirty);
    
    try {
      await Promise.all(
        dirtyTabs.map(tab => saveTabContent(tab.id))
      );
      
      saveAllTabs();
    } catch (error) {
      console.error('Failed to save all tabs:', error);
      throw error;
    }
  }, [tabs, saveTabContent, saveAllTabs]);

  const updateTabContentAndMarkDirty = useCallback((tabId: string, content: string) => {
    const tab = getTabById(tabId);
    if (!tab) return;

    updateTabContent(tabId, content);
    
    // Mark as dirty if content has changed from the original
    const file = getFileById(tab.fileId);
    const isDirty = file ? content !== file.content : true;
    markTabDirty(tabId, isDirty);
  }, [getTabById, updateTabContent, markTabDirty, getFileById]);

  // Tab navigation
  const switchToNextTab = useCallback(() => {
    if (tabs.length === 0) return;

    const currentIndex = tabs.findIndex(tab => tab.id === activeTabId);
    const nextIndex = (currentIndex + 1) % tabs.length;
    setActiveTab(tabs[nextIndex].id);
  }, [tabs, activeTabId, setActiveTab]);

  const switchToPreviousTab = useCallback(() => {
    if (tabs.length === 0) return;

    const currentIndex = tabs.findIndex(tab => tab.id === activeTabId);
    const prevIndex = currentIndex === 0 ? tabs.length - 1 : currentIndex - 1;
    setActiveTab(tabs[prevIndex].id);
  }, [tabs, activeTabId, setActiveTab]);

  const switchToTabByIndex = useCallback((index: number) => {
    if (index >= 0 && index < tabs.length) {
      setActiveTab(tabs[index].id);
    }
  }, [tabs, setActiveTab]);

  // Tab reordering
  const moveTabToIndex = useCallback((tabId: string, newIndex: number) => {
    const currentIndex = tabs.findIndex(tab => tab.id === tabId);
    if (currentIndex === -1 || newIndex < 0 || newIndex >= tabs.length) return;

    moveTab(currentIndex, newIndex);
  }, [tabs, moveTab]);

  // Editor settings helpers
  const increaseFontSize = useCallback(() => {
    setFontSize(fontSize + 1);
  }, [fontSize, setFontSize]);

  const decreaseFontSize = useCallback(() => {
    setFontSize(fontSize - 1);
  }, [fontSize, setFontSize]);

  const resetFontSize = useCallback(() => {
    setFontSize(14);
  }, [setFontSize]);

  // Utility functions
  const getTabsCount = useCallback(() => tabs.length, [tabs]);

  const getDirtyTabsCount = useCallback(() => {
    return tabs.filter(tab => tab.isDirty).length;
  }, [tabs]);

  const hasUnsavedChanges = useCallback(() => {
    return tabs.some(tab => tab.isDirty);
  }, [tabs]);

  const getTabIndex = useCallback((tabId: string) => {
    return tabs.findIndex(tab => tab.id === tabId);
  }, [tabs]);

  const isTabActive = useCallback((tabId: string) => {
    return activeTabId === tabId;
  }, [activeTabId]);

  // Tab grouping (for future enhancement)
  const getTabsByLanguage = useCallback(() => {
    const grouped = new Map<string, Tab[]>();
    
    tabs.forEach(tab => {
      const language = tab.language || 'text';
      if (!grouped.has(language)) {
        grouped.set(language, []);
      }
      grouped.get(language)!.push(tab);
    });

    return grouped;
  }, [tabs]);

  return {
    // State
    activeTabId,
    tabs,
    fontSize,
    theme,
    wordWrap,
    showLineNumbers,
    showMinimap,

    // Tab operations
    openFileInTab,
    closeTab,
    closeTabWithConfirmation,
    closeAllTabs,
    closeOtherTabs,
    setActiveTab,
    duplicateTab,

    // Content management
    updateTabContentAndMarkDirty,
    saveTabContent,
    saveAllTabsContent,
    markTabDirty,

    // Navigation
    switchToNextTab,
    switchToPreviousTab,
    switchToTabByIndex,
    moveTabToIndex,

    // Editor settings
    setFontSize,
    increaseFontSize,
    decreaseFontSize,
    resetFontSize,
    setTheme,
    toggleWordWrap,
    toggleLineNumbers,
    toggleMinimap,

    // Cursor management
    updateCursorPosition,

    // Utility
    getActiveTab,
    getTabById,
    getTabByFileId,
    getTabsCount,
    getDirtyTabsCount,
    hasUnsavedChanges,
    getTabIndex,
    isTabActive,
    getTabsByLanguage,
  };
}

// Helper function to determine language from file extension
function getLanguageFromExtension(extension?: string): string {
  if (!extension) return 'text';
  
  const languageMap: Record<string, string> = {
    'js': 'javascript',
    'jsx': 'javascript',
    'ts': 'typescript',
    'tsx': 'typescript',
    'py': 'python',
    'html': 'html',
    'css': 'css',
    'scss': 'scss',
    'sass': 'sass',
    'json': 'json',
    'md': 'markdown',
    'xml': 'xml',
    'yaml': 'yaml',
    'yml': 'yaml',
    'sql': 'sql',
    'sh': 'shell',
    'bash': 'shell',
    'php': 'php',
    'java': 'java',
    'c': 'c',
    'cpp': 'cpp',
    'cs': 'csharp',
    'go': 'go',
    'rs': 'rust',
    'rb': 'ruby',
    'swift': 'swift',
    'kt': 'kotlin',
    'dart': 'dart',
  };

  return languageMap[extension] || 'text';
}
