import { useCallback } from 'react';
import { useFileSystemStore } from '../stores/file-system-store';
import { useTabStore } from '../stores/tab-store';
import { FileNode } from '../types';

export function useFileSystem() {
  const {
    rootNode,
    selectedFileId,
    expandedDirectories,
    searchQuery,
    searchResults,
    setSelectedFile,
    toggleDirectory,
    expandDirectory,
    collapseDirectory,
    setSearchQuery,
    searchFiles,
    clearSearch,
    createFile,
    createDirectory,
    deleteFile,
    renameFile,
    updateFileContent,
    getFileById,
    getFileByPath,
    refreshFileTree,
    isDirectoryExpanded,
  } = useFileSystemStore();

  const { openTab, getTabByFileId } = useTabStore();

  // Enhanced file operations with tab integration
  const openFile = useCallback((file: FileNode) => {
    if (file.type === 'file') {
      setSelectedFile(file.id);
      
      // Check if file is already open in a tab
      const existingTab = getTabByFileId(file.id);
      if (!existingTab) {
        // Determine language from file extension
        const extension = file.name.split('.').pop()?.toLowerCase();
        const language = getLanguageFromExtension(extension);
        
        openTab(file.id, file.name, file.path, file.content || '', language);
      }
    } else if (file.type === 'directory') {
      toggleDirectory(file.id);
    }
  }, [setSelectedFile, getTabByFileId, openTab, toggleDirectory]);

  const createNewFile = useCallback(async (parentId: string, name: string, content = '') => {
    try {
      const file = createFile(parentId, name, content);
      
      // Auto-open the new file
      openFile(file);
      
      return file;
    } catch (error) {
      console.error('Failed to create file:', error);
      throw error;
    }
  }, [createFile, openFile]);

  const createNewDirectory = useCallback(async (parentId: string, name: string) => {
    try {
      const directory = createDirectory(parentId, name);
      
      // Auto-expand the parent directory
      expandDirectory(parentId);
      
      return directory;
    } catch (error) {
      console.error('Failed to create directory:', error);
      throw error;
    }
  }, [createDirectory, expandDirectory]);

  const deleteFileOrDirectory = useCallback(async (fileId: string) => {
    try {
      const file = getFileById(fileId);
      if (!file) return;

      // Close tab if file is open
      const tab = getTabByFileId(fileId);
      if (tab) {
        const { closeTab } = useTabStore.getState();
        closeTab(tab.id);
      }

      deleteFile(fileId);
    } catch (error) {
      console.error('Failed to delete file:', error);
      throw error;
    }
  }, [deleteFile, getFileById, getTabByFileId]);

  const renameFileOrDirectory = useCallback(async (fileId: string, newName: string) => {
    try {
      const file = getFileById(fileId);
      if (!file) return;

      renameFile(fileId, newName);

      // Update tab title if file is open
      const tab = getTabByFileId(fileId);
      if (tab) {
        // The tab store will need to be updated to handle file renames
        // This could be done through a subscription or event system
      }
    } catch (error) {
      console.error('Failed to rename file:', error);
      throw error;
    }
  }, [renameFile, getFileById, getTabByFileId]);

  // File tree navigation
  const expandAll = useCallback(() => {
    const expandRecursive = (node: FileNode) => {
      if (node.type === 'directory') {
        expandDirectory(node.id);
        node.children?.forEach(expandRecursive);
      }
    };

    if (rootNode) {
      expandRecursive(rootNode);
    }
  }, [rootNode, expandDirectory]);

  const collapseAll = useCallback(() => {
    const collapseRecursive = (node: FileNode) => {
      if (node.type === 'directory') {
        collapseDirectory(node.id);
        node.children?.forEach(collapseRecursive);
      }
    };

    if (rootNode) {
      collapseRecursive(rootNode);
    }
  }, [rootNode, collapseDirectory]);

  // Search functionality
  const performSearch = useCallback((query: string) => {
    setSearchQuery(query);
  }, [setSearchQuery]);

  // File content operations
  const saveFile = useCallback(async (fileId: string, content: string) => {
    try {
      updateFileContent(fileId, content);
      
      // Mark tab as saved
      const tab = getTabByFileId(fileId);
      if (tab) {
        const { markTabDirty } = useTabStore.getState();
        markTabDirty(tab.id, false);
      }
    } catch (error) {
      console.error('Failed to save file:', error);
      throw error;
    }
  }, [updateFileContent, getTabByFileId]);

  // Utility functions
  const getSelectedFile = useCallback(() => {
    return selectedFileId ? getFileById(selectedFileId) : undefined;
  }, [selectedFileId, getFileById]);

  const getFileTree = useCallback(() => {
    return rootNode;
  }, [rootNode]);

  return {
    // State
    rootNode,
    selectedFileId,
    expandedDirectories,
    searchQuery,
    searchResults,
    
    // File operations
    openFile,
    createNewFile,
    createNewDirectory,
    deleteFileOrDirectory,
    renameFileOrDirectory,
    saveFile,
    
    // Navigation
    expandDirectory,
    collapseDirectory,
    toggleDirectory,
    expandAll,
    collapseAll,
    isDirectoryExpanded,
    
    // Search
    performSearch,
    clearSearch,
    
    // Utility
    getSelectedFile,
    getFileTree,
    getFileById,
    getFileByPath,
    refreshFileTree,
  };
}

// Helper function to determine language from file extension
function getLanguageFromExtension(extension?: string): string {
  if (!extension) return 'text';
  
  const languageMap: Record<string, string> = {
    'js': 'javascript',
    'jsx': 'javascript',
    'ts': 'typescript',
    'tsx': 'typescript',
    'py': 'python',
    'html': 'html',
    'css': 'css',
    'scss': 'scss',
    'sass': 'sass',
    'json': 'json',
    'md': 'markdown',
    'xml': 'xml',
    'yaml': 'yaml',
    'yml': 'yaml',
    'sql': 'sql',
    'sh': 'shell',
    'bash': 'shell',
    'php': 'php',
    'java': 'java',
    'c': 'c',
    'cpp': 'cpp',
    'cs': 'csharp',
    'go': 'go',
    'rs': 'rust',
    'rb': 'ruby',
    'swift': 'swift',
    'kt': 'kotlin',
    'dart': 'dart',
  };

  return languageMap[extension] || 'text';
}
