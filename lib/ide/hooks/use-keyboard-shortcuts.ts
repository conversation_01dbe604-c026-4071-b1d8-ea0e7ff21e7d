import { useEffect, useCallback } from 'react';
import { useUIStore } from '../stores/ui-store';
import { useTabStore } from '../stores/tab-store';
import { useFileSystemStore } from '../stores/file-system-store';
import { useAIStore } from '../stores/ai-store';

export function useKeyboardShortcuts() {
  const { shortcuts, registerShortcut, unregisterShortcut } = useUIStore();
  const tabStore = useTabStore();
  const fileSystemStore = useFileSystemStore();
  const aiStore = useAIStore();

  //Functions
  // Helper function to switch to tab by number
  const switchToTabByNumber = useCallback((index: number) => {
    const { tabs, setActiveTab } = tabStore;
    if (tabs[index]) {
      setActiveTab(tabs[index].id);
    }
  }, [tabStore]);


  // Register default IDE shortcuts
  useEffect(() => {
    const defaultShortcuts = {
      // File operations
      'Ctrl+N': () => {
        // Create new file - would need to show a dialog
        console.log('New file shortcut');
      },
      'Ctrl+O': () => {
        // Open file - would need to show a file picker
        console.log('Open file shortcut');
      },
      'Ctrl+S': () => {
        const activeTab = tabStore.getActiveTab();
        if (activeTab) {
          tabStore.saveTab(activeTab.id);
        }
      },
      'Ctrl+Shift+S': () => {
        tabStore.saveAllTabs();
      },

      // Tab operations
      'Ctrl+W': () => {
        const activeTab = tabStore.getActiveTab();
        if (activeTab) {
          tabStore.closeTab(activeTab.id);
        }
      },
      'Ctrl+Shift+W': () => {
        tabStore.closeAllTabs();
      },
      'Ctrl+Tab': () => {
        // Switch to next tab (would need custom implementation)
        const { tabs, activeTabId, setActiveTab } = tabStore;
        const currentIndex = tabs.findIndex(tab => tab.id === activeTabId);
        const nextIndex = (currentIndex + 1) % tabs.length;
        if (tabs[nextIndex]) {
          setActiveTab(tabs[nextIndex].id);
        }
      },
      'Ctrl+Shift+Tab': () => {
        // Switch to previous tab
        const { tabs, activeTabId, setActiveTab } = tabStore;
        const currentIndex = tabs.findIndex(tab => tab.id === activeTabId);
        const prevIndex = currentIndex === 0 ? tabs.length - 1 : currentIndex - 1;
        if (tabs[prevIndex]) {
          setActiveTab(tabs[prevIndex].id);
        }
      },

      // Editor operations
      'Ctrl+Z': () => {
        // Undo - would need to implement undo/redo system
        console.log('Undo shortcut');
      },
      'Ctrl+Y': () => {
        // Redo
        console.log('Redo shortcut');
      },
      'Ctrl+F': () => {
        // Find in file
        console.log('Find shortcut');
      },
      'Ctrl+H': () => {
        // Replace in file
        console.log('Replace shortcut');
      },
      'Ctrl+Shift+F': () => {
        // Find in files
        fileSystemStore.setSearchQuery('');
      },

      // View operations
      'Ctrl+B': () => {
        useUIStore.getState().toggleSidebar();
      },
      'Ctrl+`': () => {
        useUIStore.getState().toggleTerminal();
      },
      'Ctrl+Shift+A': () => {
        useUIStore.getState().toggleAIPanel();
      },
      'F11': () => {
        const { layout, setLayout } = useUIStore.getState();
        setLayout(layout === 'zen' ? 'default' : 'zen');
      },

      // Font size
      'Ctrl+=': () => {
        tabStore.setFontSize(tabStore.fontSize + 1);
      },
      'Ctrl+-': () => {
        tabStore.setFontSize(tabStore.fontSize - 1);
      },
      'Ctrl+0': () => {
        tabStore.setFontSize(14);
      },

      // AI operations
      'Ctrl+Shift+I': () => {
        // Open AI chat
        useUIStore.getState().setAIPanelVisible(true);
      },
      'Ctrl+Shift+E': () => {
        // Explain selected code
        console.log('Explain code shortcut');
      },
      'Ctrl+Shift+G': () => {
        // Generate code
        console.log('Generate code shortcut');
      },

      // Number keys for tab switching
      'Ctrl+1': () => switchToTabByNumber(0),
      'Ctrl+2': () => switchToTabByNumber(1),
      'Ctrl+3': () => switchToTabByNumber(2),
      'Ctrl+4': () => switchToTabByNumber(3),
      'Ctrl+5': () => switchToTabByNumber(4),
      'Ctrl+6': () => switchToTabByNumber(5),
      'Ctrl+7': () => switchToTabByNumber(6),
      'Ctrl+8': () => switchToTabByNumber(7),
      'Ctrl+9': () => {
        // Switch to last tab
        const { tabs, setActiveTab } = tabStore;
        if (tabs.length > 0) {
          setActiveTab(tabs[tabs.length - 1].id);
        }
      },
    };

    // Register all shortcuts
    Object.entries(defaultShortcuts).forEach(([key, action]) => {
      registerShortcut(key, action);
    });

    // Cleanup on unmount
    return () => {
      Object.keys(defaultShortcuts).forEach(key => {
        unregisterShortcut(key);
      });
    };
  }, [registerShortcut, unregisterShortcut, tabStore, fileSystemStore, aiStore, switchToTabByNumber]);

  // Global keyboard event handler
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const key = buildKeyString(event);
      const action = shortcuts.get(key);
      
      if (action) {
        event.preventDefault();
        event.stopPropagation();
        action();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [shortcuts]);

  // Custom shortcut registration
  const addCustomShortcut = useCallback((key: string, action: () => void, description?: string) => {
    registerShortcut(key, action);
  }, [registerShortcut]);

  const removeCustomShortcut = useCallback((key: string) => {
    unregisterShortcut(key);
  }, [unregisterShortcut]);

  // Get all registered shortcuts
  const getAllShortcuts = useCallback(() => {
    return Array.from(shortcuts.entries()).map(([key, action]) => ({
      key,
      action,
      description: getShortcutDescription(key),
    }));
  }, [shortcuts]);

  return {
    addCustomShortcut,
    removeCustomShortcut,
    getAllShortcuts,
  };
}

// Helper function to build key string from keyboard event
function buildKeyString(event: KeyboardEvent): string {
  const parts: string[] = [];
  
  if (event.ctrlKey || event.metaKey) parts.push('Ctrl');
  if (event.shiftKey) parts.push('Shift');
  if (event.altKey) parts.push('Alt');
  
  // Handle special keys
  const specialKeys: Record<string, string> = {
    'Backquote': '`',
    'Equal': '=',
    'Minus': '-',
    'Tab': 'Tab',
    'Enter': 'Enter',
    'Escape': 'Escape',
    'Space': 'Space',
    'ArrowUp': 'ArrowUp',
    'ArrowDown': 'ArrowDown',
    'ArrowLeft': 'ArrowLeft',
    'ArrowRight': 'ArrowRight',
  };

  const key = specialKeys[event.code] || event.key;
  parts.push(key);
  
  return parts.join('+');
}

// Helper function to get shortcut descriptions
function getShortcutDescription(key: string): string {
  const descriptions: Record<string, string> = {
    'Ctrl+N': 'New file',
    'Ctrl+O': 'Open file',
    'Ctrl+S': 'Save file',
    'Ctrl+Shift+S': 'Save all files',
    'Ctrl+W': 'Close tab',
    'Ctrl+Shift+W': 'Close all tabs',
    'Ctrl+Tab': 'Next tab',
    'Ctrl+Shift+Tab': 'Previous tab',
    'Ctrl+Z': 'Undo',
    'Ctrl+Y': 'Redo',
    'Ctrl+F': 'Find in file',
    'Ctrl+H': 'Replace in file',
    'Ctrl+Shift+F': 'Find in files',
    'Ctrl+B': 'Toggle sidebar',
    'Ctrl+`': 'Toggle terminal',
    'Ctrl+Shift+A': 'Toggle AI panel',
    'F11': 'Toggle zen mode',
    'Ctrl+=': 'Increase font size',
    'Ctrl+-': 'Decrease font size',
    'Ctrl+0': 'Reset font size',
    'Ctrl+Shift+I': 'Open AI chat',
    'Ctrl+Shift+E': 'Explain code',
    'Ctrl+Shift+G': 'Generate code',
    'Ctrl+1': 'Switch to tab 1',
    'Ctrl+2': 'Switch to tab 2',
    'Ctrl+3': 'Switch to tab 3',
    'Ctrl+4': 'Switch to tab 4',
    'Ctrl+5': 'Switch to tab 5',
    'Ctrl+6': 'Switch to tab 6',
    'Ctrl+7': 'Switch to tab 7',
    'Ctrl+8': 'Switch to tab 8',
    'Ctrl+9': 'Switch to last tab',
  };

  return descriptions[key] || 'Custom shortcut';
}
