import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { AIMessage, AIConversation } from '../types';
import { nanoid } from 'nanoid';

interface AIStore {
  // State
  conversations: AIConversation[];
  activeConversationId?: string;
  isLoading: boolean;
  isConnected: boolean;
  
  // Actions
  createConversation: (title?: string) => AIConversation;
  deleteConversation: (conversationId: string) => void;
  setActiveConversation: (conversationId: string) => void;
  sendMessage: (content: string, context?: AIMessage['context']) => Promise<void>;
  addMessage: (conversationId: string, message: Omit<AIMessage, 'id' | 'timestamp'>) => void;
  clearConversation: (conversationId: string) => void;
  
  // Utility methods
  getActiveConversation: () => AIConversation | undefined;
  getConversationById: (conversationId: string) => AIConversation | undefined;
  
  // AI-specific actions
  generateCode: (prompt: string, language?: string) => Promise<string>;
  explainCode: (code: string, language?: string) => Promise<string>;
  refactorCode: (code: string, instructions: string, language?: string) => Promise<string>;
  findBugs: (code: string, language?: string) => Promise<string>;
  generateTests: (code: string, language?: string) => Promise<string>;
  
  // Settings
  model: string;
  temperature: number;
  maxTokens: number;
  setModel: (model: string) => void;
  setTemperature: (temperature: number) => void;
  setMaxTokens: (maxTokens: number) => void;
}

export const useAIStore = create<AIStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    conversations: [],
    activeConversationId: undefined,
    isLoading: false,
    isConnected: true,
    model: 'gpt-4',
    temperature: 0.7,
    maxTokens: 2048,

    // Actions
    createConversation: (title = 'New Conversation') => {
      const conversation: AIConversation = {
        id: nanoid(),
        title,
        messages: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      set(state => ({
        conversations: [...state.conversations, conversation],
        activeConversationId: conversation.id,
      }));

      return conversation;
    },

    deleteConversation: (conversationId) => {
      set(state => ({
        conversations: state.conversations.filter(c => c.id !== conversationId),
        activeConversationId: state.activeConversationId === conversationId 
          ? undefined 
          : state.activeConversationId,
      }));
    },

    setActiveConversation: (conversationId) => {
      set({ activeConversationId: conversationId });
    },

    sendMessage: async (content, context) => {
      const { activeConversationId, conversations } = get();
      
      if (!activeConversationId) {
        // Create a new conversation if none is active
        const newConversation = get().createConversation();
        get().setActiveConversation(newConversation.id);
      }

      const conversationId = get().activeConversationId!;
      
      // Add user message
      const userMessage: AIMessage = {
        id: nanoid(),
        role: 'user',
        content,
        timestamp: new Date(),
        context,
      };

      get().addMessage(conversationId, userMessage);
      set({ isLoading: true });

      try {
        // Simulate AI response (replace with actual AI integration)
        const response = await simulateAIResponse(content, context);
        
        const assistantMessage: AIMessage = {
          id: nanoid(),
          role: 'assistant',
          content: response,
          timestamp: new Date(),
        };

        get().addMessage(conversationId, assistantMessage);
      } catch (error) {
        console.error('AI response error:', error);
        
        const errorMessage: AIMessage = {
          id: nanoid(),
          role: 'assistant',
          content: 'Sorry, I encountered an error while processing your request.',
          timestamp: new Date(),
        };

        get().addMessage(conversationId, errorMessage);
      } finally {
        set({ isLoading: false });
      }
    },

    addMessage: (conversationId, message) => {
      const fullMessage: AIMessage = {
        ...message,
        id: nanoid(),
        timestamp: new Date(),
      };

      set(state => ({
        conversations: state.conversations.map(conv =>
          conv.id === conversationId
            ? {
                ...conv,
                messages: [...conv.messages, fullMessage],
                updatedAt: new Date(),
              }
            : conv
        ),
      }));
    },

    clearConversation: (conversationId) => {
      set(state => ({
        conversations: state.conversations.map(conv =>
          conv.id === conversationId
            ? { ...conv, messages: [], updatedAt: new Date() }
            : conv
        ),
      }));
    },

    // Utility methods
    getActiveConversation: () => {
      const { conversations, activeConversationId } = get();
      return conversations.find(c => c.id === activeConversationId);
    },

    getConversationById: (conversationId) => {
      return get().conversations.find(c => c.id === conversationId);
    },

    // AI-specific actions
    generateCode: async (prompt, language = 'javascript') => {
      set({ isLoading: true });
      try {
        // Simulate code generation
        const code = await simulateCodeGeneration(prompt, language);
        return code;
      } finally {
        set({ isLoading: false });
      }
    },

    explainCode: async (code, language = 'javascript') => {
      set({ isLoading: true });
      try {
        // Simulate code explanation
        const explanation = await simulateCodeExplanation(code, language);
        return explanation;
      } finally {
        set({ isLoading: false });
      }
    },

    refactorCode: async (code, instructions, language = 'javascript') => {
      set({ isLoading: true });
      try {
        // Simulate code refactoring
        const refactoredCode = await simulateCodeRefactoring(code, instructions, language);
        return refactoredCode;
      } finally {
        set({ isLoading: false });
      }
    },

    findBugs: async (code, language = 'javascript') => {
      set({ isLoading: true });
      try {
        // Simulate bug finding
        const bugs = await simulateBugFinding(code, language);
        return bugs;
      } finally {
        set({ isLoading: false });
      }
    },

    generateTests: async (code, language = 'javascript') => {
      set({ isLoading: true });
      try {
        // Simulate test generation
        const tests = await simulateTestGeneration(code, language);
        return tests;
      } finally {
        set({ isLoading: false });
      }
    },

    // Settings
    setModel: (model) => {
      set({ model });
    },

    setTemperature: (temperature) => {
      set({ temperature: Math.max(0, Math.min(2, temperature)) });
    },

    setMaxTokens: (maxTokens) => {
      set({ maxTokens: Math.max(1, Math.min(4096, maxTokens)) });
    },
  }))
);

// Simulation functions (replace with actual AI integration)
async function simulateAIResponse(content: string, context?: AIMessage['context']): Promise<string> {
  await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
  
  if (context?.selectedText) {
    return `I can help you with the selected code: "${context.selectedText}". ${content}`;
  }
  
  return `I understand you want to: ${content}. Here's my response...`;
}

async function simulateCodeGeneration(prompt: string, language: string): Promise<string> {
  await new Promise(resolve => setTimeout(resolve, 1500));
  return `// Generated ${language} code for: ${prompt}\nfunction example() {\n  // Implementation here\n}`;
}

async function simulateCodeExplanation(code: string, language: string): Promise<string> {
  await new Promise(resolve => setTimeout(resolve, 1000));
  return `This ${language} code does the following:\n\n1. Defines a function\n2. Implements logic\n3. Returns a result`;
}

async function simulateCodeRefactoring(code: string, instructions: string, language: string): Promise<string> {
  await new Promise(resolve => setTimeout(resolve, 1500));
  return `// Refactored ${language} code based on: ${instructions}\n${code}\n// Improvements applied`;
}

async function simulateBugFinding(code: string, language: string): Promise<string> {
  await new Promise(resolve => setTimeout(resolve, 1200));
  return `Potential issues found in your ${language} code:\n\n1. Missing error handling\n2. Possible null reference\n3. Performance optimization opportunity`;
}

async function simulateTestGeneration(code: string, language: string): Promise<string> {
  await new Promise(resolve => setTimeout(resolve, 1800));
  return `// Generated tests for your ${language} code\ndescribe('Test Suite', () => {\n  it('should work correctly', () => {\n    // Test implementation\n  });\n});`;
}
