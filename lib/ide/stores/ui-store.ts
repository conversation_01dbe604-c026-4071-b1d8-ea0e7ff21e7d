import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { UIState, TerminalSession } from '../types';
import { nanoid } from 'nanoid';

interface UIStore extends UIState {
  // Terminal state
  terminalSessions: TerminalSession[];
  activeTerminalId?: string;
  
  // Actions - Layout
  toggleSidebar: () => void;
  setSidebarVisible: (visible: boolean) => void;
  setSidebarWidth: (width: number) => void;
  
  toggleTerminal: () => void;
  setTerminalVisible: (visible: boolean) => void;
  setTerminalHeight: (height: number) => void;
  
  toggleAIPanel: () => void;
  setAIPanelVisible: (visible: boolean) => void;
  setAIPanelWidth: (width: number) => void;
  
  toggleStatusBar: () => void;
  setStatusBarVisible: (visible: boolean) => void;
  
  setLayout: (layout: UIState['layout']) => void;
  
  // Terminal actions
  createTerminalSession: (title?: string) => TerminalSession;
  closeTerminalSession: (sessionId: string) => void;
  setActiveTerminal: (sessionId: string) => void;
  addToTerminalHistory: (sessionId: string, command: string) => void;
  clearTerminalHistory: (sessionId: string) => void;
  
  // Utility methods
  getActiveTerminalSession: () => TerminalSession | undefined;
  getTerminalSessionById: (sessionId: string) => TerminalSession | undefined;
  
  // Keyboard shortcuts
  shortcuts: Map<string, () => void>;
  registerShortcut: (key: string, action: () => void) => void;
  unregisterShortcut: (key: string) => void;
  
  // Theme and preferences
  theme: 'light' | 'dark' | 'auto';
  setTheme: (theme: 'light' | 'dark' | 'auto') => void;
  
  // Notifications
  notifications: Array<{
    id: string;
    type: 'info' | 'success' | 'warning' | 'error';
    title: string;
    message: string;
    timestamp: Date;
    duration?: number;
  }>;
  addNotification: (notification: Omit<UIStore['notifications'][0], 'id' | 'timestamp'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
}

export const useUIStore = create<UIStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    sidebarVisible: true,
    sidebarWidth: 300,
    terminalVisible: false,
    terminalHeight: 200,
    aiPanelVisible: false,
    aiPanelWidth: 400,
    statusBarVisible: true,
    layout: 'default',
    
    // Terminal state
    terminalSessions: [],
    activeTerminalId: undefined,
    
    // Theme
    theme: 'dark',
    
    // Notifications
    notifications: [],
    
    // Shortcuts
    shortcuts: new Map(),

    // Layout actions
    toggleSidebar: () => {
      set(state => ({ sidebarVisible: !state.sidebarVisible }));
    },

    setSidebarVisible: (visible) => {
      set({ sidebarVisible: visible });
    },

    setSidebarWidth: (width) => {
      set({ sidebarWidth: Math.max(200, Math.min(600, width)) });
    },

    toggleTerminal: () => {
      set(state => ({ terminalVisible: !state.terminalVisible }));
    },

    setTerminalVisible: (visible) => {
      set({ terminalVisible: visible });
      
      // Create a default terminal session if none exists and terminal is being shown
      if (visible && get().terminalSessions.length === 0) {
        get().createTerminalSession();
      }
    },

    setTerminalHeight: (height) => {
      set({ terminalHeight: Math.max(100, Math.min(500, height)) });
    },

    toggleAIPanel: () => {
      set(state => ({ aiPanelVisible: !state.aiPanelVisible }));
    },

    setAIPanelVisible: (visible) => {
      set({ aiPanelVisible: visible });
    },

    setAIPanelWidth: (width) => {
      set({ aiPanelWidth: Math.max(300, Math.min(800, width)) });
    },

    toggleStatusBar: () => {
      set(state => ({ statusBarVisible: !state.statusBarVisible }));
    },

    setStatusBarVisible: (visible) => {
      set({ statusBarVisible: visible });
    },

    setLayout: (layout) => {
      set({ layout });
      
      // Apply layout-specific settings
      switch (layout) {
        case 'zen':
          set({
            sidebarVisible: false,
            terminalVisible: false,
            aiPanelVisible: false,
            statusBarVisible: false,
          });
          break;
        case 'focus':
          set({
            sidebarVisible: false,
            terminalVisible: false,
            aiPanelVisible: false,
            statusBarVisible: true,
          });
          break;
        case 'default':
          set({
            sidebarVisible: true,
            statusBarVisible: true,
          });
          break;
      }
    },

    // Terminal actions
    createTerminalSession: (title = 'Terminal') => {
      const session: TerminalSession = {
        id: nanoid(),
        title: get().terminalSessions.length > 0 ? `${title} ${get().terminalSessions.length + 1}` : title,
        isActive: false,
        history: [],
        currentDirectory: '/',
      };

      // Deactivate all other sessions
      const updatedSessions = get().terminalSessions.map(s => ({ ...s, isActive: false }));
      updatedSessions.push({ ...session, isActive: true });

      set({
        terminalSessions: updatedSessions,
        activeTerminalId: session.id,
      });

      return session;
    },

    closeTerminalSession: (sessionId) => {
      const { terminalSessions, activeTerminalId } = get();
      const sessionIndex = terminalSessions.findIndex(s => s.id === sessionId);
      
      if (sessionIndex === -1) return;

      const newSessions = terminalSessions.filter(s => s.id !== sessionId);
      let newActiveId = activeTerminalId;

      // If closing the active session, select another one
      if (activeTerminalId === sessionId && newSessions.length > 0) {
        const nextSession = newSessions[sessionIndex] || newSessions[newSessions.length - 1];
        newActiveId = nextSession.id;
        
        // Update the new active session
        const updatedSessions = newSessions.map(s => ({
          ...s,
          isActive: s.id === newActiveId,
        }));

        set({
          terminalSessions: updatedSessions,
          activeTerminalId: newActiveId,
        });
      } else {
        set({
          terminalSessions: newSessions,
          activeTerminalId: newSessions.length === 0 ? undefined : newActiveId,
        });
      }
    },

    setActiveTerminal: (sessionId) => {
      const updatedSessions = get().terminalSessions.map(s => ({
        ...s,
        isActive: s.id === sessionId,
      }));

      set({
        terminalSessions: updatedSessions,
        activeTerminalId: sessionId,
      });
    },

    addToTerminalHistory: (sessionId, command) => {
      const updatedSessions = get().terminalSessions.map(s =>
        s.id === sessionId
          ? { ...s, history: [...s.history, command] }
          : s
      );

      set({ terminalSessions: updatedSessions });
    },

    clearTerminalHistory: (sessionId) => {
      const updatedSessions = get().terminalSessions.map(s =>
        s.id === sessionId
          ? { ...s, history: [] }
          : s
      );

      set({ terminalSessions: updatedSessions });
    },

    // Utility methods
    getActiveTerminalSession: () => {
      const { terminalSessions, activeTerminalId } = get();
      return terminalSessions.find(s => s.id === activeTerminalId);
    },

    getTerminalSessionById: (sessionId) => {
      return get().terminalSessions.find(s => s.id === sessionId);
    },

    // Keyboard shortcuts
    registerShortcut: (key, action) => {
      const shortcuts = new Map(get().shortcuts);
      shortcuts.set(key, action);
      set({ shortcuts });
    },

    unregisterShortcut: (key) => {
      const shortcuts = new Map(get().shortcuts);
      shortcuts.delete(key);
      set({ shortcuts });
    },

    // Theme
    setTheme: (theme) => {
      set({ theme });
    },

    // Notifications
    addNotification: (notification) => {
      const newNotification = {
        ...notification,
        id: nanoid(),
        timestamp: new Date(),
      };

      set(state => ({
        notifications: [...state.notifications, newNotification],
      }));

      // Auto-remove notification after duration
      if (notification.duration) {
        setTimeout(() => {
          get().removeNotification(newNotification.id);
        }, notification.duration);
      }
    },

    removeNotification: (id) => {
      set(state => ({
        notifications: state.notifications.filter(n => n.id !== id),
      }));
    },

    clearNotifications: () => {
      set({ notifications: [] });
    },
  }))
);

// Setup default keyboard shortcuts
const store = useUIStore.getState();

store.registerShortcut('Ctrl+B', store.toggleSidebar);
store.registerShortcut('Ctrl+`', store.toggleTerminal);
store.registerShortcut('Ctrl+Shift+A', store.toggleAIPanel);
store.registerShortcut('F11', () => store.setLayout(store.layout === 'zen' ? 'default' : 'zen'));
