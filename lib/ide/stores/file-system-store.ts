import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { FileNode, FileSystemState } from '../types';
import { memoryFS } from '../memory-fs';

interface FileSystemStore extends FileSystemState {
  // Actions
  setSelectedFile: (fileId: string | undefined) => void;
  toggleDirectory: (directoryId: string) => void;
  expandDirectory: (directoryId: string) => void;
  collapseDirectory: (directoryId: string) => void;
  setSearchQuery: (query: string) => void;
  searchFiles: (query: string) => void;
  clearSearch: () => void;
  
  // File operations
  createFile: (parentId: string, name: string, content?: string) => FileNode;
  createDirectory: (parentId: string, name: string) => FileNode;
  deleteFile: (fileId: string) => void;
  renameFile: (fileId: string, newName: string) => void;
  updateFileContent: (fileId: string, content: string) => void;
  
  // Utility methods
  getFileById: (fileId: string) => FileNode | undefined;
  getFileByPath: (path: string) => FileNode | undefined;
  refreshFileTree: () => void;
  isDirectoryExpanded: (directoryId: string) => boolean;
}

export const useFileSystemStore = create<FileSystemStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    rootNode: memoryFS.getRoot(),
    selectedFileId: undefined,
    expandedDirectories: new Set<string>(),
    searchQuery: '',
    searchResults: [],

    // Actions
    setSelectedFile: (fileId) => {
      set({ selectedFileId: fileId });
    },

    toggleDirectory: (directoryId) => {
      const { expandedDirectories } = get();
      const newExpanded = new Set(expandedDirectories);
      
      if (newExpanded.has(directoryId)) {
        newExpanded.delete(directoryId);
      } else {
        newExpanded.add(directoryId);
      }
      
      set({ expandedDirectories: newExpanded });
    },

    expandDirectory: (directoryId) => {
      const { expandedDirectories } = get();
      const newExpanded = new Set(expandedDirectories);
      newExpanded.add(directoryId);
      set({ expandedDirectories: newExpanded });
    },

    collapseDirectory: (directoryId) => {
      const { expandedDirectories } = get();
      const newExpanded = new Set(expandedDirectories);
      newExpanded.delete(directoryId);
      set({ expandedDirectories: newExpanded });
    },

    setSearchQuery: (query) => {
      set({ searchQuery: query });
      if (query.trim()) {
        get().searchFiles(query);
      } else {
        get().clearSearch();
      }
    },

    searchFiles: (query) => {
      const results = memoryFS.searchFiles(query);
      set({ searchResults: results });
    },

    clearSearch: () => {
      set({ searchQuery: '', searchResults: [] });
    },

    // File operations
    createFile: (parentId, name, content = '') => {
      const file = memoryFS.createFile(parentId, name, content);
      set({ rootNode: memoryFS.getRoot() });
      return file;
    },

    createDirectory: (parentId, name) => {
      const directory = memoryFS.createDirectory(parentId, name);
      set({ rootNode: memoryFS.getRoot() });
      // Auto-expand parent directory
      get().expandDirectory(parentId);
      return directory;
    },

    deleteFile: (fileId) => {
      memoryFS.deleteFile(fileId);
      set({ 
        rootNode: memoryFS.getRoot(),
        selectedFileId: get().selectedFileId === fileId ? undefined : get().selectedFileId
      });
    },

    renameFile: (fileId, newName) => {
      memoryFS.renameFile(fileId, newName);
      set({ rootNode: memoryFS.getRoot() });
    },

    updateFileContent: (fileId, content) => {
      memoryFS.updateFile(fileId, content);
      set({ rootNode: memoryFS.getRoot() });
    },

    // Utility methods
    getFileById: (fileId) => {
      return memoryFS.getFile(fileId);
    },

    getFileByPath: (path) => {
      return memoryFS.getFileByPath(path);
    },

    refreshFileTree: () => {
      set({ rootNode: memoryFS.getRoot() });
    },

    isDirectoryExpanded: (directoryId) => {
      return get().expandedDirectories.has(directoryId);
    },
  }))
);

// Subscribe to file system events
memoryFS.addEventListener((event) => {
  const store = useFileSystemStore.getState();
  
  switch (event.type) {
    case 'file-created':
    case 'directory-created':
    case 'file-updated':
    case 'file-deleted':
    case 'file-renamed':
    case 'filesystem-imported':
      store.refreshFileTree();
      break;
  }
});
