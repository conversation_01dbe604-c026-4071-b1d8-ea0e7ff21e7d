import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { Tab, EditorState } from '../types';
import { nanoid } from 'nanoid';

interface TabStore extends EditorState {
  // Actions
  openTab: (fileId: string, title: string, path: string, content: string, language?: string) => void;
  closeTab: (tabId: string) => void;
  closeAllTabs: () => void;
  closeOtherTabs: (tabId: string) => void;
  setActiveTab: (tabId: string) => void;
  updateTabContent: (tabId: string, content: string) => void;
  markTabDirty: (tabId: string, isDirty: boolean) => void;
  saveTab: (tabId: string) => void;
  saveAllTabs: () => void;
  
  // Tab management
  moveTab: (fromIndex: number, toIndex: number) => void;
  duplicateTab: (tabId: string) => void;
  getTabById: (tabId: string) => Tab | undefined;
  getTabByFileId: (fileId: string) => Tab | undefined;
  getActiveTab: () => Tab | undefined;
  
  // Editor settings
  setFontSize: (size: number) => void;
  setTheme: (theme: 'light' | 'dark') => void;
  toggleWordWrap: () => void;
  toggleLineNumbers: () => void;
  toggleMinimap: () => void;
  
  // Cursor and selection
  updateCursorPosition: (tabId: string, line: number, column: number) => void;
}

export const useTabStore = create<TabStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    activeTabId: undefined,
    tabs: [],
    fontSize: 14,
    theme: 'dark',
    wordWrap: true,
    showLineNumbers: true,
    showMinimap: false,

    // Actions
    openTab: (fileId, title, path, content, language) => {
      const { tabs } = get();
      
      // Check if tab is already open
      const existingTab = tabs.find(tab => tab.fileId === fileId);
      if (existingTab) {
        get().setActiveTab(existingTab.id);
        return;
      }

      const newTab: Tab = {
        id: nanoid(),
        fileId,
        title,
        path,
        isDirty: false,
        isActive: false,
        content,
        language,
        cursorPosition: { line: 1, column: 1 },
      };

      // Deactivate all other tabs
      const updatedTabs = tabs.map(tab => ({ ...tab, isActive: false }));
      updatedTabs.push({ ...newTab, isActive: true });

      set({
        tabs: updatedTabs,
        activeTabId: newTab.id,
      });
    },

    closeTab: (tabId) => {
      const { tabs, activeTabId } = get();
      const tabIndex = tabs.findIndex(tab => tab.id === tabId);
      
      if (tabIndex === -1) return;

      const newTabs = tabs.filter(tab => tab.id !== tabId);
      let newActiveTabId = activeTabId;

      // If closing the active tab, select another tab
      if (activeTabId === tabId && newTabs.length > 0) {
        // Select the tab to the right, or the last tab if closing the rightmost
        const nextTab = newTabs[tabIndex] || newTabs[newTabs.length - 1];
        newActiveTabId = nextTab.id;
        
        // Update the new active tab
        const updatedTabs = newTabs.map(tab => ({
          ...tab,
          isActive: tab.id === newActiveTabId,
        }));

        set({
          tabs: updatedTabs,
          activeTabId: newActiveTabId,
        });
      } else {
        set({
          tabs: newTabs,
          activeTabId: newTabs.length === 0 ? undefined : newActiveTabId,
        });
      }
    },

    closeAllTabs: () => {
      set({
        tabs: [],
        activeTabId: undefined,
      });
    },

    closeOtherTabs: (tabId) => {
      const { tabs } = get();
      const tabToKeep = tabs.find(tab => tab.id === tabId);
      
      if (tabToKeep) {
        set({
          tabs: [{ ...tabToKeep, isActive: true }],
          activeTabId: tabId,
        });
      }
    },

    setActiveTab: (tabId) => {
      const { tabs } = get();
      const updatedTabs = tabs.map(tab => ({
        ...tab,
        isActive: tab.id === tabId,
      }));

      set({
        tabs: updatedTabs,
        activeTabId: tabId,
      });
    },

    updateTabContent: (tabId, content) => {
      const { tabs } = get();
      const updatedTabs = tabs.map(tab => 
        tab.id === tabId 
          ? { ...tab, content, isDirty: tab.content !== content }
          : tab
      );

      set({ tabs: updatedTabs });
    },

    markTabDirty: (tabId, isDirty) => {
      const { tabs } = get();
      const updatedTabs = tabs.map(tab => 
        tab.id === tabId ? { ...tab, isDirty } : tab
      );

      set({ tabs: updatedTabs });
    },

    saveTab: (tabId) => {
      const { tabs } = get();
      const updatedTabs = tabs.map(tab => 
        tab.id === tabId ? { ...tab, isDirty: false } : tab
      );

      set({ tabs: updatedTabs });
    },

    saveAllTabs: () => {
      const { tabs } = get();
      const updatedTabs = tabs.map(tab => ({ ...tab, isDirty: false }));
      set({ tabs: updatedTabs });
    },

    // Tab management
    moveTab: (fromIndex, toIndex) => {
      const { tabs } = get();
      const newTabs = [...tabs];
      const [movedTab] = newTabs.splice(fromIndex, 1);
      newTabs.splice(toIndex, 0, movedTab);
      set({ tabs: newTabs });
    },

    duplicateTab: (tabId) => {
      const { tabs } = get();
      const tabToDuplicate = tabs.find(tab => tab.id === tabId);
      
      if (tabToDuplicate) {
        const duplicatedTab: Tab = {
          ...tabToDuplicate,
          id: nanoid(),
          title: `${tabToDuplicate.title} (Copy)`,
          isActive: false,
        };

        const tabIndex = tabs.findIndex(tab => tab.id === tabId);
        const newTabs = [...tabs];
        newTabs.splice(tabIndex + 1, 0, duplicatedTab);

        set({ tabs: newTabs });
      }
    },

    getTabById: (tabId) => {
      return get().tabs.find(tab => tab.id === tabId);
    },

    getTabByFileId: (fileId) => {
      return get().tabs.find(tab => tab.fileId === fileId);
    },

    getActiveTab: () => {
      const { tabs, activeTabId } = get();
      return tabs.find(tab => tab.id === activeTabId);
    },

    // Editor settings
    setFontSize: (size) => {
      set({ fontSize: Math.max(8, Math.min(32, size)) });
    },

    setTheme: (theme) => {
      set({ theme });
    },

    toggleWordWrap: () => {
      set(state => ({ wordWrap: !state.wordWrap }));
    },

    toggleLineNumbers: () => {
      set(state => ({ showLineNumbers: !state.showLineNumbers }));
    },

    toggleMinimap: () => {
      set(state => ({ showMinimap: !state.showMinimap }));
    },

    // Cursor and selection
    updateCursorPosition: (tabId, line, column) => {
      const { tabs } = get();
      const updatedTabs = tabs.map(tab => 
        tab.id === tabId 
          ? { ...tab, cursorPosition: { line, column } }
          : tab
      );

      set({ tabs: updatedTabs });
    },
  }))
);
