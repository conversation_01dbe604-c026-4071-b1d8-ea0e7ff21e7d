import { mount } from '@stlite/browser';

export interface StreamlitProject {
  files: Record<string, string>;
  entryPoint: string;
  title: string;
  description: string;
}

export interface StreamlitExecution {
  id: string;
  projectId: string;
  status: 'starting' | 'running' | 'stopped' | 'error';
  mountElement?: HTMLElement;
  unmountFunction?: () => void;
  error?: string;
  logs: Array<{
    id: string;
    type: 'stdout' | 'stderr' | 'info';
    message: string;
    timestamp: Date;
  }>;
}

export interface ExecutionOutput {
  type: 'stdout' | 'stderr' | 'info';
  data: string;
  timestamp: Date;
}

class StreamlitClientExecutionManager {
  private executions = new Map<string, StreamlitExecution>();
  private logCallbacks = new Map<string, (output: ExecutionOutput) => void>();

  /**
   * Start a Streamlit application in the browser using stlite
   */
  async startStreamlitApp(
    projectId: string,
    project: StreamlitProject,
    mountElement: HTMLElement,
    onOutput?: (output: ExecutionOutput) => void
  ): Promise<StreamlitExecution> {
    // Stop existing execution if running
    await this.stopExecution(projectId);

    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    
    const execution: StreamlitExecution = {
      id: executionId,
      projectId,
      status: 'starting',
      mountElement,
      logs: [],
    };

    this.executions.set(projectId, execution);
    
    if (onOutput) {
      this.logCallbacks.set(projectId, onOutput);
    }

    try {
      this.addLog(projectId, 'info', 'Starting Streamlit application...');

      // Extract requirements from requirements.txt if it exists
      const requirements = this.extractRequirements(project.files);
      
      // Prepare stlite mount options
      const mountOptions = {
        requirements,
        entrypoint: project.entryPoint,
        files: project.files,
        streamlitConfig: {
          'client.toolbarMode': 'viewer',
          'client.showErrorDetails': true,
          'theme.base': 'light',
        },
      };

      this.addLog(projectId, 'info', `Mounting Streamlit app with ${requirements.length} requirements...`);

      // Mount the Streamlit app using stlite
      const mountResult = mount(mountOptions, mountElement);
      execution.unmountFunction = mountResult.unmount;

      execution.status = 'running';
      this.addLog(projectId, 'info', 'Streamlit application started successfully');

      return execution;

    } catch (error) {
      execution.status = 'error';
      execution.error = error instanceof Error ? error.message : 'Unknown error';
      this.addLog(projectId, 'stderr', `Error starting application: ${execution.error}`);
      throw error;
    }
  }

  /**
   * Stop a running Streamlit execution
   */
  async stopExecution(projectId: string): Promise<void> {
    const execution = this.executions.get(projectId);
    if (execution?.unmountFunction) {
      try {
        // Unmount the stlite app
        execution.unmountFunction();
        execution.status = 'stopped';
        this.addLog(projectId, 'info', 'Streamlit application stopped');
      } catch (error) {
        this.addLog(projectId, 'stderr', `Error stopping application: ${error}`);
      }
    }
    this.executions.delete(projectId);
    this.logCallbacks.delete(projectId);
  }

  /**
   * Update project files and restart if needed
   */
  async updateProject(
    projectId: string,
    project: StreamlitProject,
    onOutput?: (output: ExecutionOutput) => void
  ): Promise<StreamlitExecution> {
    const existingExecution = this.executions.get(projectId);
    
    if (existingExecution?.mountElement && existingExecution.status === 'running') {
      this.addLog(projectId, 'info', 'Updating Streamlit application...');
      return this.startStreamlitApp(projectId, project, existingExecution.mountElement, onOutput);
    }
    
    throw new Error('No running execution found to update');
  }

  /**
   * Get execution status
   */
  getExecution(projectId: string): StreamlitExecution | undefined {
    return this.executions.get(projectId);
  }

  /**
   * Get all running executions
   */
  getAllExecutions(): StreamlitExecution[] {
    return Array.from(this.executions.values());
  }

  /**
   * Extract requirements from project files
   */
  private extractRequirements(files: Record<string, string>): string[] {
    const requirementsFile = files['requirements.txt'];
    if (!requirementsFile) {
      return ['streamlit']; // Default requirement
    }

    return requirementsFile
      .split('\n')
      .map(line => line.trim())
      .filter(line => line && !line.startsWith('#'))
      .map(line => {
        // Handle version specifiers (e.g., "pandas>=1.0.0" -> "pandas")
        const packageName = line.split(/[>=<!=~]/)[0].trim();
        return packageName;
      });
  }

  /**
   * Add a log entry and notify callback
   */
  private addLog(projectId: string, type: 'stdout' | 'stderr' | 'info', message: string): void {
    const execution = this.executions.get(projectId);
    if (!execution) return;

    const logEntry = {
      id: `log_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      type,
      message,
      timestamp: new Date(),
    };

    execution.logs.push(logEntry);

    // Notify callback
    const callback = this.logCallbacks.get(projectId);
    if (callback) {
      callback({
        type,
        data: message,
        timestamp: logEntry.timestamp,
      });
    }
  }

  /**
   * Clean up all executions
   */
  async cleanup(): Promise<void> {
    const projectIds = Array.from(this.executions.keys());
    for (const projectId of projectIds) {
      await this.stopExecution(projectId);
    }
  }
}

// Export singleton instance
export const streamlitClientExecutionManager = new StreamlitClientExecutionManager();

// Cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    streamlitClientExecutionManager.cleanup();
  });
}
