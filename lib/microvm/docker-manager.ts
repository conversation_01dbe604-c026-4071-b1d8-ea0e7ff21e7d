import { exec } from 'child_process';
import { promisify } from 'util';
import { nanoid } from 'nanoid';
import { MicroVM, MicroVMCreateOptions, MicroVMManager, MicroVMStatus } from './types';

const execAsync = promisify(exec);

export class DockerMicroVMManager implements MicroVMManager {
  private vms: Map<string, MicroVM> = new Map();

  constructor() {
    // In a production system, we'd initialize from a database
  }

  async createVM(options: MicroVMCreateOptions): Promise<MicroVM> {
    const id = nanoid();
    const containerName = `microvm-${id}`;
    
    // Set default values if not provided
    const memory = options.memory || 512; // Default 512MB
    const cpu = options.cpu || 1; // Default 1 CPU
    const storage = options.storage || 5; // Default 5GB
    
    // Build port mappings
    const portMappings = (options.ports || []).map(port => `-p ${port}:${port}`).join(' ');
    
    // Build environment variable mappings
    const envVars = Object.entries(options.envVars || {})
      .map(([key, value]) => `-e ${key}="${value}"`)
      .join(' ');
    
    try {
      // Create and start the Docker container
      const cmd = `docker run -d --name ${containerName} ${portMappings} ${envVars} --memory=${memory}m --cpus=${cpu} ${options.image}`;
      
      const { stdout, stderr } = await execAsync(cmd);
      
      if (stderr && !stdout) {
        throw new Error(`Failed to create container: ${stderr}`);
      }
      
      const vm: MicroVM = {
        id,
        name: options.name,
        status: 'running',
        createdAt: new Date(),
        updatedAt: new Date(),
        memory,
        cpu,
        storage,
        image: options.image,
        ports: options.ports || [],
        envVars: options.envVars || {},
        userId: 'system', // In a real app, this would come from authentication
      };
      
      this.vms.set(id, vm);
      return vm;
    } catch (error: any) {
      console.error('Error creating Docker container:', error);
      throw new Error(`Failed to create VM: ${error.message}`);
    }
  }

  async getVM(id: string): Promise<MicroVM | null> {
    const vm = this.vms.get(id);
    if (!vm) return null;

    try {
      // Update status from Docker
      const { stdout } = await execAsync(`docker inspect --format='{{.State.Status}}' microvm-${id}`);
      
      let status: MicroVMStatus = 'stopped';
      if (stdout.trim() === 'running') status = 'running';
      else if (stdout.trim() === 'paused') status = 'paused';
      else if (stdout.trim() === 'created') status = 'creating';
      else if (stdout.trim() === 'exited') status = 'stopped';
      
      return { ...vm, status, updatedAt: new Date() };
    } catch (error: any) {
      console.error(`Error getting VM status: ${error.message}`);
      return vm; // Return cached data if Docker command fails
    }
  }

  async listVMs(userId: string): Promise<MicroVM[]> {
    // In a real application, filter by userId from the database
    return Array.from(this.vms.values())
      .filter(vm => vm.userId === userId || vm.userId === 'system');
  }

  async startVM(id: string): Promise<MicroVM> {
    const vm = await this.getVM(id);
    if (!vm) throw new Error(`VM with id ${id} not found`);
    
    try {
      await execAsync(`docker start microvm-${id}`);
      
      const updatedVM = { 
        ...vm, 
        status: 'running' as MicroVMStatus,
        updatedAt: new Date() 
      };
      
      this.vms.set(id, updatedVM);
      return updatedVM;
    } catch (error: any) {
      console.error(`Error starting VM: ${error.message}`);
      throw new Error(`Failed to start VM: ${error.message}`);
    }
  }

  async stopVM(id: string): Promise<MicroVM> {
    const vm = await this.getVM(id);
    if (!vm) throw new Error(`VM with id ${id} not found`);
    
    try {
      await execAsync(`docker stop microvm-${id}`);
      
      const updatedVM = { 
        ...vm, 
        status: 'stopped' as MicroVMStatus,
        updatedAt: new Date() 
      };
      
      this.vms.set(id, updatedVM);
      return updatedVM;
    } catch (error: any) {
      console.error(`Error stopping VM: ${error.message}`);
      throw new Error(`Failed to stop VM: ${error.message}`);
    }
  }

  async deleteVM(id: string): Promise<void> {
    const vm = await this.getVM(id);
    if (!vm) throw new Error(`VM with id ${id} not found`);
    
    try {
      // Stop the container if it's running
      if (vm.status === 'running' || vm.status === 'paused') {
        await execAsync(`docker stop microvm-${id}`);
      }
      
      // Remove the container
      await execAsync(`docker rm microvm-${id}`);
      
      // Remove from our map
      this.vms.delete(id);
    } catch (error: any) {
      console.error(`Error deleting VM: ${error.message}`);
      throw new Error(`Failed to delete VM: ${error.message}`);
    }
  }

  async executeCommand(id: string, command: string): Promise<{ stdout: string; stderr: string; exitCode: number }> {
    const vm = await this.getVM(id);
    if (!vm) throw new Error(`VM with id ${id} not found`);
    
    if (vm.status !== 'running') {
      throw new Error(`VM is not running (current status: ${vm.status})`);
    }
    
    try {
      // Execute command in the container
      const { stdout, stderr } = await execAsync(`docker exec microvm-${id} ${command}`);
      
      return { 
        stdout, 
        stderr, 
        exitCode: 0  // We don't have direct access to exit code from promisified exec
      };
    } catch (error: any) {
      console.error(`Error executing command in VM: ${error.message}`);
      return {
        stdout: '',
        stderr: error.message,
        exitCode: 1
      };
    }
  }
} 