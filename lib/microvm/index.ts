// Export types
export * from './types';

// Export managers
export { DockerMicroVMManager } from './docker-manager';
export { InMemoryAgentManager } from './agent-manager';

// Export database implementations
export * from './db';

// Export orchestration
export { MicroVMOrchestration } from './orchestration';

// Create a default instance of orchestration that can be imported and used
import { MicroVMOrchestration } from './orchestration';

// In-memory implementation (default)
export const orchestration = new MicroVMOrchestration();

// Database-backed implementation (optional)
export const dbOrchestration = new MicroVMOrchestration({ useDatabase: true }); 