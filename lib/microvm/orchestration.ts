import { DockerMicroVMManager } from './docker-manager';
import { InMemoryAgentManager } from './agent-manager';
import { Agent, MicroVM, MicroVMCreateOptions } from './types';
import { DbAgentManager, DbMicroVMManager } from './db';

/**
 * Orchestration service that manages the relationship between
 * AI agents and their execution environments (MicroVMs)
 */
export class MicroVMOrchestration {
  // Exposed managers for direct access when needed
  public vmManager: DockerMicroVMManager | DbMicroVMManager;
  public agentManager: InMemoryAgentManager | DbAgentManager;
  
  constructor(options: { useDatabase?: boolean } = {}) {
    const { useDatabase = false } = options;
    
    // Use either in-memory or database-backed implementations
    if (useDatabase) {
      this.vmManager = new DbMicroVMManager();
      this.agentManager = new DbAgentManager();
    } else {
      this.vmManager = new DockerMicroVMManager();
      this.agentManager = new InMemoryAgentManager();
    }
  }

  /**
   * Creates a new agent with its own dedicated execution environment
   */
  async createAgentWithEnvironment(
    agentOptions: {
      name: string;
      role: 'developer' | 'designer' | 'tester' | 'analyst' | 'custom';
      description: string;
      systemPrompt: string;
      tools?: string[];
    },
    vmOptions: {
      image: string;
      memory?: number;
      cpu?: number;
      ports?: number[];
      envVars?: Record<string, string>;
    }
  ): Promise<{ agent: Agent; vm: MicroVM }> {
    // First create the VM
    const vmCreateOptions: MicroVMCreateOptions = {
      name: `${agentOptions.name}-vm`,
      image: vmOptions.image,
      memory: vmOptions.memory,
      cpu: vmOptions.cpu,
      ports: vmOptions.ports,
      envVars: vmOptions.envVars,
    };

    const vm = await this.vmManager.createVM(vmCreateOptions);

    // Then create the agent
    const agent = await this.agentManager.createAgent({
      name: agentOptions.name,
      role: agentOptions.role,
      description: agentOptions.description,
      systemPrompt: agentOptions.systemPrompt,
      tools: agentOptions.tools,
      microVMId: vm.id,
    });

    return { agent, vm };
  }

  /**
   * Lists all agents with their associated VMs
   */
  async listAgentsWithEnvironments(userId: string): Promise<Array<{ agent: Agent; vm: MicroVM | null }>> {
    const agents = await this.agentManager.listAgents(userId);
    
    const results = await Promise.all(
      agents.map(async (agent) => {
        if (!agent.microVMId) {
          return { agent, vm: null };
        }
        
        const vm = await this.vmManager.getVM(agent.microVMId);
        return { agent, vm };
      })
    );
    
    return results;
  }

  /**
   * Execute a command in an agent's VM and return the result
   */
  async executeAgentCommand(agentId: string, command: string): Promise<{ stdout: string; stderr: string; exitCode: number }> {
    const agent = await this.agentManager.getAgent(agentId);
    if (!agent) {
      throw new Error(`Agent with id ${agentId} not found`);
    }
    
    if (!agent.microVMId) {
      throw new Error(`Agent ${agent.name} does not have an associated MicroVM`);
    }
    
    return this.vmManager.executeCommand(agent.microVMId, command);
  }

  /**
   * Start an agent's VM
   */
  async startAgentVM(agentId: string): Promise<MicroVM> {
    const agent = await this.agentManager.getAgent(agentId);
    if (!agent) {
      throw new Error(`Agent with id ${agentId} not found`);
    }
    
    if (!agent.microVMId) {
      throw new Error(`Agent ${agent.name} does not have an associated MicroVM`);
    }
    
    return this.vmManager.startVM(agent.microVMId);
  }

  /**
   * Stop an agent's VM
   */
  async stopAgentVM(agentId: string): Promise<MicroVM> {
    const agent = await this.agentManager.getAgent(agentId);
    if (!agent) {
      throw new Error(`Agent with id ${agentId} not found`);
    }
    
    if (!agent.microVMId) {
      throw new Error(`Agent ${agent.name} does not have an associated MicroVM`);
    }
    
    return this.vmManager.stopVM(agent.microVMId);
  }

  /**
   * Delete an agent and its associated VM
   */
  async deleteAgentWithEnvironment(agentId: string): Promise<void> {
    const agent = await this.agentManager.getAgent(agentId);
    if (!agent) {
      throw new Error(`Agent with id ${agentId} not found`);
    }
    
    // Delete the VM if one exists
    if (agent.microVMId) {
      await this.vmManager.deleteVM(agent.microVMId);
    }
    
    // Delete the agent
    await this.agentManager.deleteAgent(agentId);
  }
} 