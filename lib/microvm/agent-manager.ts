import { nanoid } from 'nanoid';
import { Agent, AgentCreateOptions, AgentManager } from './types';

export class InMemoryAgentManager implements AgentManager {
  private agents: Map<string, Agent> = new Map();

  constructor() {
    // In a production system, we'd initialize from a database
  }

  async createAgent(options: AgentCreateOptions): Promise<Agent> {
    const id = nanoid();
    
    const agent: Agent = {
      id,
      name: options.name,
      role: options.role,
      description: options.description,
      systemPrompt: options.systemPrompt,
      tools: options.tools || [],
      microVMId: options.microVMId,
      createdAt: new Date(),
      updatedAt: new Date(),
      userId: 'system', // In a real app, this would come from authentication
    };
    
    this.agents.set(id, agent);
    return agent;
  }

  async getAgent(id: string): Promise<Agent | null> {
    return this.agents.get(id) || null;
  }

  async listAgents(userId: string): Promise<Agent[]> {
    // In a real application, filter by userId from the database
    return Array.from(this.agents.values())
      .filter(agent => agent.userId === userId || agent.userId === 'system');
  }

  async updateAgent(id: string, updates: Partial<AgentCreateOptions>): Promise<Agent> {
    const agent = await this.getAgent(id);
    if (!agent) throw new Error(`Agent with id ${id} not found`);
    
    const updatedAgent: Agent = {
      ...agent,
      ...updates,
      updatedAt: new Date()
    };
    
    this.agents.set(id, updatedAgent);
    return updatedAgent;
  }

  async deleteAgent(id: string): Promise<void> {
    if (!this.agents.has(id)) {
      throw new Error(`Agent with id ${id} not found`);
    }
    
    this.agents.delete(id);
  }

  async assignVMToAgent(agentId: string, microVMId: string): Promise<Agent> {
    const agent = await this.getAgent(agentId);
    if (!agent) throw new Error(`Agent with id ${agentId} not found`);
    
    const updatedAgent: Agent = {
      ...agent,
      microVMId,
      updatedAt: new Date()
    };
    
    this.agents.set(agentId, updatedAgent);
    return updatedAgent;
  }
} 