# MicroVM Orchestration System

This module provides a Docker-based MicroVM (Micro Virtual Machine) orchestration system for AI agents. It allows creating, managing, and executing commands in isolated environments for AI agents.

## Architecture

The system is built using a layered architecture:

1. **Core Types Layer**: Defines the interfaces and types used throughout the system.
2. **Manager Layer**: Provides implementations for managing VMs and Agents.
3. **Orchestration Layer**: Coordinates the relationship between agents and their execution environments.
4. **Database Layer**: Persists agent and VM information in a PostgreSQL database.

## Components

### Types

- `MicroVM`: Represents a virtual machine environment.
- `Agent`: Represents an AI agent with specific capabilities.
- `MicroVMManager`: Interface for managing VM lifecycle.
- `AgentManager`: Interface for managing agent lifecycle.

### Implementations

- `DockerMicroVMManager`: Manages Docker containers as VM environments.
- `InMemoryAgentManager`: In-memory implementation for agent management.
- `DbMicroVMManager`: Database-backed implementation for VM management.
- `DbAgentManager`: Database-backed implementation for agent management.

### Orchestration

`MicroVMOrchestration`: Coordinates the relationship between agents and their execution environments.

## Usage

### Creating an Agent with Environment

```typescript
import { dbOrchestration } from '@/lib/microvm';

// Create an agent with its own VM
const { agent, vm } = await dbOrchestration.createAgentWithEnvironment(
  {
    name: 'Developer Agent',
    role: 'developer',
    description: 'Full stack developer specialized in web applications',
    systemPrompt: 'You are a helpful developer assistant.',
    tools: ['code-review', 'debugging'],
  },
  {
    image: 'node:18',
    memory: 1024,
    cpu: 1,
    ports: [3000],
    envVars: { NODE_ENV: 'development' },
  }
);
```

### Executing Commands in Agent's VM

```typescript
// Execute a command in the agent's VM
const result = await dbOrchestration.executeAgentCommand(
  agent.id,
  'node -e "console.log(\'Hello from VM\')"'
);

console.log(result.stdout); // "Hello from VM"
```

### Managing VM Lifecycle

```typescript
// Start an agent's VM
await dbOrchestration.startAgentVM(agent.id);

// Stop an agent's VM
await dbOrchestration.stopAgentVM(agent.id);

// Delete an agent and its VM
await dbOrchestration.deleteAgentWithEnvironment(agent.id);
```

## Database Setup

To set up the database for the MicroVM system:

1. Ensure you have a PostgreSQL database available.
2. Set the `DATABASE_URL` environment variable.
3. Run the migration script:

```bash
npm run setup-microvm-db
```

To seed the database with default agents:

```bash
npm run seed-microvm-db
```

## Security Considerations

- The system uses Docker containers for isolation, but additional security measures should be implemented for production use.
- User authentication and authorization should be properly implemented.
- Resource limits should be enforced to prevent abuse.

## Future Enhancements

- Support for additional VM backends (e.g., WebAssembly, Firecracker)
- Enhanced resource monitoring and management
- Better integration with AI models for agent autonomy
- Support for agent-to-agent communication 