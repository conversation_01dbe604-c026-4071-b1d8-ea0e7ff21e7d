export type MicroVMStatus = 'creating' | 'running' | 'paused' | 'stopped' | 'failed';

export interface MicroVM {
  id: string;
  name: string;
  status: MicroVMStatus;
  createdAt: Date;
  updatedAt: Date;
  memory: number; // Memory in MB
  cpu: number; // CPU cores
  storage: number; // Storage in GB
  image: string; // Docker image used for the VM
  ports: number[]; // Exposed ports
  envVars: Record<string, string>; // Environment variables
  userId: string; // Owner of the VM
}

export interface MicroVMCreateOptions {
  name: string;
  memory?: number;
  cpu?: number;
  storage?: number;
  image: string;
  ports?: number[];
  envVars?: Record<string, string>;
}

export interface MicroVMManager {
  createVM(options: MicroVMCreateOptions): Promise<MicroVM>;
  getVM(id: string): Promise<MicroVM | null>;
  listVMs(userId: string): Promise<MicroVM[]>;
  startVM(id: string): Promise<MicroVM>;
  stopVM(id: string): Promise<MicroVM>;
  deleteVM(id: string): Promise<void>;
  executeCommand(id: string, command: string): Promise<{ stdout: string; stderr: string; exitCode: number }>;
}

export type AgentRole = 'developer' | 'designer' | 'tester' | 'analyst' | 'custom';

export interface Agent {
  id: string;
  name: string;
  role: AgentRole;
  description: string;
  systemPrompt: string;
  tools?: string[];
  microVMId?: string;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  // VM information when returned from API
  vm?: {
    id: string;
    name: string;
    status: MicroVMStatus;
    image?: string;
    memory?: number;
    cpu?: number;
  };
}

export interface AgentCreateOptions {
  name: string;
  role: AgentRole;
  description: string;
  systemPrompt: string;
  tools?: string[];
  microVMId?: string;
}

export interface AgentManager {
  createAgent(options: AgentCreateOptions): Promise<Agent>;
  getAgent(id: string): Promise<Agent | null>;
  listAgents(userId: string): Promise<Agent[]>;
  updateAgent(id: string, updates: Partial<AgentCreateOptions>): Promise<Agent>;
  deleteAgent(id: string): Promise<void>;
  assignVMToAgent(agentId: string, microVMId: string): Promise<Agent>;
}

export interface Tool {
  id: string;
  name: string;
  description: string;
  schema: Record<string, any>; // JSONSchema for tool parameters
  handler: string; // Function name or path to execute 
  createdAt: Date;
  updatedAt: Date;
  userId: string;
} 