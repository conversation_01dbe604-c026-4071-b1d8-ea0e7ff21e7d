import { nanoid } from 'nanoid';
import { exec } from 'child_process';
import { promisify } from 'util';
import { prisma } from '@/lib/db/prisma';
import { MicroVM, MicroVMCreateOptions, MicroVMManager, MicroVMStatus } from '../types';

const execAsync = promisify(exec);

export class DbMicroVMManager implements MicroVMManager {
  async createVM(options: MicroVMCreateOptions): Promise<MicroVM> {
    const id = nanoid();
    const containerName = `microvm-${id}`;
    
    // Set default values if not provided
    const memory = options.memory || 512;
    const cpu = options.cpu || 1;
    const storage = options.storage || 5;
    
    // Build port mappings
    const portMappings = (options.ports || []).map(port => `-p ${port}:${port}`).join(' ');
    
    // Build environment variable mappings
    const envVars = Object.entries(options.envVars || {})
      .map(([key, value]) => `-e ${key}="${value}"`)
      .join(' ');
    
    try {
      // Create and start the Docker container
      const cmd = `docker run -d --name ${containerName} ${portMappings} ${envVars} --memory=${memory}m --cpus=${cpu} ${options.image}`;
      
      const { stdout, stderr } = await execAsync(cmd);
      
      if (stderr && !stdout) {
        throw new Error(`Failed to create container: ${stderr}`);
      }
      
      // Create the VM record in the database
      const vmData = {
        id,
        name: options.name,
        status: 'running' as MicroVMStatus,
        createdAt: new Date(),
        updatedAt: new Date(),
        memory,
        cpu,
        storage,
        image: options.image,
        ports: options.ports || [],
        envVars: options.envVars || {},
        userId: 'system', // In a real app, this would come from authentication
      };
      
      await prisma.microVM.create({ data: vmData });
      
      // Return the VM we just created
      return this.getVM(id) as Promise<MicroVM>;
    } catch (error: any) {
      console.error('Error creating Docker container:', error);
      throw new Error(`Failed to create VM: ${error.message}`);
    }
  }

  async getVM(id: string): Promise<MicroVM | null> {
    const vm = await prisma.microVM.findUnique({
      where: { id },
    });

    if (!vm) {
      return null;
    }
    
    try {
      // Update status from Docker
      const { stdout } = await execAsync(`docker inspect --format='{{.State.Status}}' microvm-${id}`);
      
      let status: MicroVMStatus = 'stopped';
      if (stdout.trim() === 'running') status = 'running';
      else if (stdout.trim() === 'paused') status = 'paused';
      else if (stdout.trim() === 'created') status = 'creating';
      else if (stdout.trim() === 'exited') status = 'stopped';
      
      // If status has changed, update in database
      if (status !== vm.status) {
        await prisma.microVM.update({
          where: { id },
          data: {
            status,
            updatedAt: new Date()
          },
        });

        vm.status = status;
        vm.updatedAt = new Date();
      }
      
      // Transform the database row to MicroVM interface
      return {
        id: vm.id,
        name: vm.name,
        status: vm.status as MicroVMStatus,
        createdAt: vm.createdAt,
        updatedAt: vm.updatedAt,
        memory: vm.memory,
        cpu: vm.cpu,
        storage: vm.storage,
        image: vm.image,
        ports: vm.ports as number[],
        envVars: vm.envVars as Record<string, string>,
        userId: vm.userId,
      };
    } catch (error: any) {
      console.error(`Error getting VM status: ${error.message}`);
      
      // If Docker command fails, return cached data from DB
      return {
        id: vm.id,
        name: vm.name,
        status: vm.status as MicroVMStatus,
        createdAt: vm.createdAt,
        updatedAt: vm.updatedAt,
        memory: vm.memory,
        cpu: vm.cpu,
        storage: vm.storage,
        image: vm.image,
        ports: vm.ports as number[],
        envVars: vm.envVars as Record<string, string>,
        userId: vm.userId,
      };
    }
  }

  async listVMs(userId: string): Promise<MicroVM[]> {
    const result = await prisma.microVM.findMany({
      where: { userId },
    });
    
    // Transform the database rows to MicroVM interface
    return result.map(vm => ({
      id: vm.id,
      name: vm.name,
      status: vm.status as MicroVMStatus,
      createdAt: vm.createdAt,
      updatedAt: vm.updatedAt,
      memory: vm.memory,
      cpu: vm.cpu,
      storage: vm.storage,
      image: vm.image,
      ports: vm.ports as number[],
      envVars: vm.envVars as Record<string, string>,
      userId: vm.userId,
    }));
  }

  async startVM(id: string): Promise<MicroVM> {
    const vm = await this.getVM(id);
    if (!vm) throw new Error(`VM with id ${id} not found`);
    
    try {
      await execAsync(`docker start microvm-${id}`);
      
      // Update status in database
      await prisma.microVM.update({
        where: { id },
        data: {
          status: 'running',
          updatedAt: new Date()
        },
      });
      
      // Return updated VM
      return this.getVM(id) as Promise<MicroVM>;
    } catch (error: any) {
      console.error(`Error starting VM: ${error.message}`);
      throw new Error(`Failed to start VM: ${error.message}`);
    }
  }

  async stopVM(id: string): Promise<MicroVM> {
    const vm = await this.getVM(id);
    if (!vm) throw new Error(`VM with id ${id} not found`);
    
    try {
      await execAsync(`docker stop microvm-${id}`);
      
      // Update status in database
      await prisma.microVM.update({
        where: { id },
        data: {
          status: 'stopped',
          updatedAt: new Date()
        },
      });
      
      // Return updated VM
      return this.getVM(id) as Promise<MicroVM>;
    } catch (error: any) {
      console.error(`Error stopping VM: ${error.message}`);
      throw new Error(`Failed to stop VM: ${error.message}`);
    }
  }

  async deleteVM(id: string): Promise<void> {
    const vm = await this.getVM(id);
    if (!vm) throw new Error(`VM with id ${id} not found`);
    
    try {
      // Stop the container if it's running
      if (vm.status === 'running' || vm.status === 'paused') {
        await execAsync(`docker stop microvm-${id}`);
      }
      
      // Remove the container
      await execAsync(`docker rm -f microvm-${id}`);
      
      // Delete from database
      await prisma.microVM.delete({
        where: { id },
      });
    } catch (error: any) {
      console.error(`Error deleting VM: ${error.message}`);
      throw new Error(`Failed to delete VM: ${error.message}`);
    }
  }

  async executeCommand(id: string, command: string): Promise<{ stdout: string; stderr: string; exitCode: number }> {
    const vm = await this.getVM(id);
    if (!vm) throw new Error(`VM with id ${id} not found`);
    
    if (vm.status !== 'running') {
      throw new Error(`VM is not running (current status: ${vm.status})`);
    }
    
    try {
      // Execute command in the container
      const { stdout, stderr } = await execAsync(`docker exec microvm-${id} ${command}`);
      
      return { 
        stdout, 
        stderr, 
        exitCode: 0  // We don't have direct access to exit code from promisified exec
      };
    } catch (error: any) {
      console.error(`Error executing command in VM: ${error.message}`);
      return {
        stdout: '',
        stderr: error.message,
        exitCode: 1
      };
    }
  }
} 