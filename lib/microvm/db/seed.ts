import { dbOrchestration } from '../index';

export async function seedAgents() {
  console.log('Seeding default agents...');
  
  try {
    // Developer Agent with Node.js environment
    await dbOrchestration.createAgentWithEnvironment(
      {
        name: 'Developer Agent',
        role: 'developer',
        description: 'Full stack developer specialized in web applications',
        systemPrompt: 'You are a helpful developer assistant. You provide code samples, debug issues, and explain technical concepts.',
        tools: ['code-review', 'debugging'],
      },
      {
        image: 'node:18',
        memory: 1024,
        cpu: 1,
        ports: [3000],
        envVars: { NODE_ENV: 'development' },
      }
    );
    
    // Data Analysis Agent with Python environment
    await dbOrchestration.createAgentWithEnvironment(
      {
        name: 'Data Analyst',
        role: 'analyst',
        description: 'Data analysis expert specialized in processing large datasets',
        systemPrompt: 'You are a data analysis assistant. You help with data processing, visualization, and insights extraction.',
        tools: ['data-processing', 'visualization'],
      },
      {
        image: 'python:3.9',
        memory: 2048,
        cpu: 2,
        ports: [8888],
        envVars: { PYTHONPATH: '/app' },
      }
    );
    
    // Testing Agent with Ubuntu environment
    await dbOrchestration.createAgentWithEnvironment(
      {
        name: 'QA Tester',
        role: 'tester',
        description: 'Software QA specialist focused on automated testing',
        systemPrompt: 'You are a QA assistant. You help with test design, validation, and quality assurance processes.',
        tools: ['test-runner', 'test-design'],
      },
      {
        image: 'ubuntu:latest',
        memory: 512,
        cpu: 1,
        ports: [],
        envVars: {},
      }
    );
    
    console.log('Default agents seeded successfully!');
  } catch (error) {
    console.error('Error seeding agents:', error);
    throw error;
  }
}

// Main function to seed database
async function main() {
  try {
    await seedAgents();
    console.log('All data seeded successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Failed to seed database:', error);
    process.exit(1);
  }
}

// Run seeding when executed directly
if (require.main === module) {
  main();
}

// Export for use in other files
export { main as seedDatabase }; 