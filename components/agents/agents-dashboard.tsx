"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { Agent } from '@/lib/microvm/types';
import { Skeleton } from '@/components/ui/skeleton';

interface VMMetrics {
  id: string;
  name: string;
  cpuUsage: number[];
  memoryUsage: number[];
  timestamps: string[];
}

export function AgentsDashboard() {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [metrics, setMetrics] = useState<Record<string, VMMetrics>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Fetch the agents list
    async function fetchAgents() {
      try {
        const response = await fetch('/api/agents');
        if (!response.ok) {
          throw new Error('Failed to fetch agents');
        }
        
        const data = await response.json();
        setAgents(data.agents);
        
        // Initialize metrics for each agent with VM
        const initialMetrics: Record<string, VMMetrics> = {};
        data.agents.forEach((agent: any) => {
          if (agent.vm) {
            initialMetrics[agent.id] = {
              id: agent.vm.id,
              name: agent.vm.name,
              cpuUsage: [],
              memoryUsage: [],
              timestamps: []
            };
          }
        });
        
        setMetrics(initialMetrics);
        setLoading(false);
      } catch (err: any) {
        setError(err.message || 'An error occurred');
        setLoading(false);
      }
    }
    
    fetchAgents();
    
    // Set up interval to fetch metrics
    const interval = setInterval(fetchMetrics, 5000);
    
    return () => {
      clearInterval(interval);
    };
  }, []);
  
  async function fetchMetrics() {
    // In a real implementation, this would fetch metrics from each VM
    // For demo purposes, we'll generate random metrics
    setMetrics(prev => {
      const updated = { ...prev };
      
      Object.keys(updated).forEach(agentId => {
        const agent = agents.find(a => a.id === agentId);
        if (agent && agent.microVMId) {
          // Generate random CPU and memory usage
          const cpuUsage = Math.random() * 100;
          const memoryUsage = Math.random() * agent.vm?.memory || 512;
          const timestamp = new Date().toLocaleTimeString();
          
          // Keep up to 10 data points
          updated[agentId].cpuUsage = [...updated[agentId].cpuUsage.slice(-9), cpuUsage];
          updated[agentId].memoryUsage = [...updated[agentId].memoryUsage.slice(-9), memoryUsage];
          updated[agentId].timestamps = [...updated[agentId].timestamps.slice(-9), timestamp];
        }
      });
      
      return updated;
    });
  }

  if (loading) {
    return <LoadingDashboard />;
  }

  if (error) {
    return (
      <div className="rounded-lg border border-red-200 bg-red-50 p-4">
        <p className="text-red-800">Error: {error}</p>
      </div>
    );
  }

  const agentsWithVMs = agents.filter(agent => agent.microVMId);

  if (agentsWithVMs.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>No Active VMs</CardTitle>
          <CardDescription>
            There are no active VMs to monitor. Create an agent with a VM to see metrics here.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {agentsWithVMs.map(agent => {
        const agentMetrics = metrics[agent.id];
        
        if (!agentMetrics) {
          return null;
        }
        
        const chartData = agentMetrics.timestamps.map((time, index) => ({
          time,
          cpu: agentMetrics.cpuUsage[index],
          memory: agentMetrics.memoryUsage[index]
        }));

        return (
          <Card key={agent.id} className="overflow-hidden">
            <CardHeader className="bg-slate-50">
              <div className="flex items-center justify-between">
                <CardTitle>{agent.name}</CardTitle>
                <VMStatusBadge status={agent.vm?.status || 'stopped'} />
              </div>
              <CardDescription>{agent.role} agent running on {agent.vm?.name}</CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <div className="h-[200px] w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={chartData} margin={{ top: 15, right: 15, left: 15, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Legend />
                    <Line yAxisId="left" type="monotone" dataKey="cpu" stroke="#8884d8" name="CPU %" />
                    <Line yAxisId="right" type="monotone" dataKey="memory" stroke="#82ca9d" name="Memory (MB)" />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
            <CardFooter className="bg-slate-50 border-t">
              <div className="w-full flex justify-between text-sm text-muted-foreground">
                <div>Image: {agent.vm?.image || 'N/A'}</div>
                <div>CPU: {agent.vm?.cpu || 1} cores</div>
                <div>Memory: {agent.vm?.memory || 512}MB</div>
              </div>
            </CardFooter>
          </Card>
        );
      })}
    </div>
  );
}

function VMStatusBadge({ status }: { status: string }) {
  let variant: "default" | "secondary" | "destructive" | "outline" = "outline";
  
  switch (status) {
    case 'running':
      variant = "default";
      break;
    case 'stopped':
      variant = "secondary";
      break;
    case 'error':
      variant = "destructive";
      break;
    default:
      variant = "outline";
  }
  
  return <Badge variant={variant}>{status}</Badge>;
}

function LoadingDashboard() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {[1, 2, 3].map((i) => (
        <Card key={i}>
          <CardHeader>
            <Skeleton className="h-5 w-1/2" />
            <Skeleton className="h-4 w-4/5" />
          </CardHeader>
          <CardContent className="p-0">
            <Skeleton className="h-[200px] w-full" />
          </CardContent>
          <CardFooter>
            <Skeleton className="h-4 w-full" />
          </CardFooter>
        </Card>
      ))}
    </div>
  );
} 