'use client';

import { useState } from 'react';
import { Agent } from '@/lib/microvm/types';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { PlayIcon, TerminalIcon } from 'lucide-react';

type CommandResponse = {
  stdout: string;
  stderr: string;
  exitCode: number;
};

interface AgentCommandDialogProps {
  agent: Agent;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function AgentCommandDialog({ agent, open, onOpenChange }: AgentCommandDialogProps) {
  const [command, setCommand] = useState<string>('');
  const [executing, setExecuting] = useState<boolean>(false);
  const [response, setResponse] = useState<CommandResponse | null>(null);
  
  const handleExecute = async () => {
    if (!command.trim()) return;
    
    setExecuting(true);
    
    try {
      // In a production app, this would be an API call
      // const response = await fetch(`/api/agents/${agent.id}/execute`, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ command })
      // });
      // const data = await response.json();
      
      // Mock response for demo
      const mockResponse = {
        stdout: `Executing '${command}' in agent VM:\n\n${generateMockOutput(command)}`,
        stderr: '',
        exitCode: 0
      };
      
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setResponse(mockResponse);
    } catch (error) {
      console.error('Error executing command:', error);
      setResponse({
        stdout: '',
        stderr: 'Failed to execute command: Network error',
        exitCode: 1
      });
    } finally {
      setExecuting(false);
    }
  };
  
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !executing) {
      handleExecute();
    }
  };
  
  // Generate mock output based on command
  function generateMockOutput(cmd: string): string {
    const lsOutput = `total 20
drwxr-xr-x 2 <USER> <GROUP> 4096 Jun 20 10:25 .
drwxr-xr-x 6 <USER> <GROUP> 4096 Jun 20 10:25 ..
-rw-r--r-- 1 <USER> <GROUP>  425 Jun 20 10:25 app.js
-rw-r--r-- 1 <USER> <GROUP>  251 Jun 20 10:25 package.json
drwxr-xr-x 2 <USER> <GROUP> 4096 Jun 20 10:25 node_modules`;
    
    const catOutput = `{
  "name": "agent-app",
  "version": "1.0.0",
  "description": "Sample application for agent VM",
  "main": "app.js",
  "scripts": {
    "start": "node app.js"
  },
  "dependencies": {
    "express": "^4.18.2"
  }
}`;

    const npmOutput = `
> agent-app@1.0.0 start
> node app.js

Server running on port 3000
`;
    
    if (cmd.startsWith('ls')) return lsOutput;
    if (cmd.startsWith('cat')) return catOutput;
    if (cmd.startsWith('npm')) return npmOutput;
    if (cmd.startsWith('node')) return 'Hello from Node.js!';
    
    return `Command '${cmd}' executed successfully.`;
  }
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TerminalIcon className="h-5 w-5" />
            Execute Command in {agent.name}
          </DialogTitle>
          <DialogDescription>
            Run commands directly in the agent&apos;s isolated VM environment.
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex items-center gap-2 my-2">
          <Input
            value={command}
            onChange={(e) => setCommand(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Enter command to execute..."
            disabled={executing}
            className="font-mono"
          />
          <Button 
            onClick={handleExecute} 
            disabled={!command.trim() || executing}
            size="sm"
          >
            <PlayIcon className="h-4 w-4 mr-1" />
            Run
          </Button>
        </div>
        
        {response && (
          <div className="mt-4">
            <div className="font-semibold text-sm flex items-center justify-between">
              <span>Output</span>
              <span className={`text-xs ${response.exitCode === 0 ? 'text-green-500' : 'text-red-500'}`}>
                Exit code: {response.exitCode}
              </span>
            </div>
            <pre className="bg-muted p-3 rounded-md mt-1 text-xs font-mono overflow-auto max-h-[300px] whitespace-pre-wrap">
              {response.stdout}
              {response.stderr && (
                <span className="text-red-500">{response.stderr}</span>
              )}
            </pre>
          </div>
        )}
        
        <DialogFooter className="mt-2">
          <Button variant="ghost" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 