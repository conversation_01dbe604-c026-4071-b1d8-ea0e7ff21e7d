'use client';

import { useState, useEffect } from 'react';
import { Agent, MicroVM } from '@/lib/microvm/types';
import { <PERSON>, <PERSON><PERSON>ontent, CardFooter, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  PlayIcon, 
  StopIcon, 
  TrashIcon, 
  TerminalIcon,
  RefreshCwIcon
} from 'lucide-react';
import { AgentCommandDialog } from './agent-command-dialog';

type AgentWithVM = {
  agent: Agent;
  vm: MicroVM | null;
};

export function AgentsList() {
  const [agents, setAgents] = useState<AgentWithVM[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [commandDialogOpen, setCommandDialogOpen] = useState<boolean>(false);
  
  // In a real app, this would fetch from an API
  useEffect(() => {
    async function fetchAgents() {
      try {
        // This is just a mock implementation for now
        // In a real app, this would be an API call to the backend
        // like: const response = await fetch('/api/agents');
        
        // Mock data for UI development
        const mockAgents: AgentWithVM[] = [
          {
            agent: {
              id: '1',
              name: 'Development Agent',
              role: 'developer',
              description: 'Helps with coding tasks and development workflows',
              systemPrompt: 'You are a development assistant...',
              tools: ['code-review', 'debugging'],
              createdAt: new Date(),
              updatedAt: new Date(),
              userId: 'system',
              microVMId: '1'
            },
            vm: {
              id: '1',
              name: 'dev-agent-vm',
              status: 'running',
              createdAt: new Date(),
              updatedAt: new Date(),
              memory: 1024,
              cpu: 1,
              storage: 5,
              image: 'node:18',
              ports: [3000],
              envVars: { NODE_ENV: 'development' },
              userId: 'system'
            }
          },
          {
            agent: {
              id: '2',
              name: 'Data Analysis Agent',
              role: 'analyst',
              description: 'Processes and analyzes data sets',
              systemPrompt: 'You are a data analyst assistant...',
              tools: ['data-processing', 'visualization'],
              createdAt: new Date(),
              updatedAt: new Date(),
              userId: 'system',
              microVMId: '2'
            },
            vm: {
              id: '2',
              name: 'data-agent-vm',
              status: 'stopped',
              createdAt: new Date(),
              updatedAt: new Date(),
              memory: 2048,
              cpu: 2,
              storage: 10,
              image: 'python:3.9',
              ports: [8888],
              envVars: { PYTHONPATH: '/app' },
              userId: 'system'
            }
          }
        ];
        
        setAgents(mockAgents);
        setLoading(false);
      } catch (error) {
        console.error('Failed to fetch agents:', error);
        setLoading(false);
      }
    }
    
    fetchAgents();
  }, []);
  
  const handleStartVM = (agentId: string) => {
    // This would call the API to start the VM
    console.log(`Starting VM for agent ${agentId}`);
    
    // Update local state (optimistic update)
    setAgents(prevAgents => 
      prevAgents.map(item => {
        if (item.agent.id === agentId && item.vm) {
          return {
            ...item,
            vm: {
              ...item.vm,
              status: 'running'
            }
          };
        }
        return item;
      })
    );
  };
  
  const handleStopVM = (agentId: string) => {
    // This would call the API to stop the VM
    console.log(`Stopping VM for agent ${agentId}`);
    
    // Update local state (optimistic update)
    setAgents(prevAgents => 
      prevAgents.map(item => {
        if (item.agent.id === agentId && item.vm) {
          return {
            ...item,
            vm: {
              ...item.vm,
              status: 'stopped'
            }
          };
        }
        return item;
      })
    );
  };
  
  const handleDeleteAgent = (agentId: string) => {
    // This would call the API to delete the agent and its VM
    console.log(`Deleting agent ${agentId}`);
    
    // Update local state
    setAgents(prevAgents => 
      prevAgents.filter(item => item.agent.id !== agentId)
    );
  };
  
  const handleOpenCommandDialog = (agent: Agent) => {
    setSelectedAgent(agent);
    setCommandDialogOpen(true);
  };
  
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCwIcon className="w-6 h-6 animate-spin" />
        <span className="ml-2">Loading agents...</span>
      </div>
    );
  }
  
  if (agents.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 border rounded-lg border-dashed p-8">
        <p className="text-muted-foreground">No agents found. Create your first agent to get started.</p>
      </div>
    );
  }
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {agents.map(({ agent, vm }) => (
        <Card key={agent.id} className="overflow-hidden">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-lg">{agent.name}</h3>
              <Badge variant={agent.role === 'developer' ? 'default' : 'secondary'}>
                {agent.role}
              </Badge>
            </div>
          </CardHeader>
          
          <CardContent className="pb-2">
            <p className="text-muted-foreground text-sm mb-2">{agent.description}</p>
            
            {vm && (
              <div className="flex flex-col gap-1 mt-3 text-xs text-muted-foreground">
                <div className="flex items-center justify-between">
                  <span>Status</span>
                  <Badge variant={vm.status === 'running' ? 'success' : 'secondary'}>
                    {vm.status}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span>Image</span>
                  <span className="font-mono text-xs">{vm.image}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span>Resources</span>
                  <span>{vm.memory}MB / {vm.cpu} CPU</span>
                </div>
              </div>
            )}
          </CardContent>
          
          <CardFooter className="flex justify-between pt-2 gap-2">
            {vm?.status === 'running' ? (
              <>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => handleOpenCommandDialog(agent)}
                >
                  <TerminalIcon className="mr-1 h-4 w-4" />
                  Execute
                </Button>
                
                <div className="flex gap-2">
                  <Button 
                    variant="destructive" 
                    size="sm" 
                    onClick={() => handleStopVM(agent.id)}
                  >
                    <StopIcon className="mr-1 h-4 w-4" />
                    Stop
                  </Button>
                </div>
              </>
            ) : (
              <>
                <Button 
                  variant="outline" 
                  size="sm" 
                  disabled={!vm} 
                  onClick={() => handleStartVM(agent.id)}
                >
                  <PlayIcon className="mr-1 h-4 w-4" />
                  Start
                </Button>
                
                <Button 
                  variant="destructive" 
                  size="sm" 
                  onClick={() => handleDeleteAgent(agent.id)}
                >
                  <TrashIcon className="mr-1 h-4 w-4" />
                  Delete
                </Button>
              </>
            )}
          </CardFooter>
        </Card>
      ))}
      
      {selectedAgent && (
        <AgentCommandDialog
          agent={selectedAgent}
          open={commandDialogOpen}
          onOpenChange={setCommandDialogOpen}
        />
      )}
    </div>
  );
} 